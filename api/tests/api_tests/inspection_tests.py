import uuid
from datetime import datetime, date, timedelta, timezone
from decimal import Decimal

import pytest
from drf_spectacular.generators import SchemaGenerator
from jsonschema import validate

from django.core.cache import caches
from django.db.models import Q
from django.test.client import Client
from django.utils import timezone as dj_timezone
from rest_framework import status

from api.tests import factory
from api.common.enums import StatusEnum, UserLevelEnum
from api.defects.models import Standard, StandardHeader
from api.inspections.gradings import update_gradings
from api.inspections.models import (
    Asset,
    Inspection,
    InspectionValue,
    JobsTree,
    FileList,
    MapPointList,
    AssetValue,
    ProcessingList,
    VideoFrames,
    InspectionFilter,
)
from api.organisations.models import Organisations
from api.tests.settings import InspectionSettings, AssetSettings, UserSettings
from api.users.models import CustomUser
from api.inspections.pydantic_models.inspection_model import InspectionModel, get_inspection_details
from api.recommendations.models import RepairRecommendation
from vapar.constants.pipes import StandardEnum, DirectionEnum, PipeTypeEnum

pytestmark = [pytest.mark.django_db(databases=["default"])]
caches["inspections"].clear()


@pytest.fixture(autouse=True)
def patch_get_storage_sas_token(monkeypatch):
    def mock_get_storage_sas_token(*args, **kwargs):
        return "mock-sas-token"

    def mock_get_storage_url(*args, **kwargs):
        return "https://mock-url.com"

    monkeypatch.setattr(
        "api.inspections.serializers.inspection_serializers.get_platform_storage_sas_token",
        mock_get_storage_sas_token,
    )
    monkeypatch.setattr("api.inspections.views.views.get_platform_storage_sas_token", mock_get_storage_sas_token)
    monkeypatch.setattr("api.inspections.views.views.get_processing_storage_sas_token", mock_get_storage_sas_token)
    monkeypatch.setattr("api.inspections.views.views.get_processing_storage_region_base_url", mock_get_storage_url)


class TestInspections:
    """
    Tests for the Inspections API

    1. GET /inspections2?organisation={organisation_id}
    2. GET /inspections2/{inspection_id}
    3. GET /inspections/filters?organisation={organisation_id}
    """

    client: Client = Client()
    product_owner: CustomUser = None
    standard_user: CustomUser = None
    root_folder: JobsTree = None
    inspections: list[InspectionModel] = []

    @pytest.fixture(autouse=True)
    def setup_method(self, product_owner, standard_user, asset_owner_org, root_folder_for_standard_user):
        self.product_owner = product_owner
        self.standard_user = standard_user
        self.root_folder = root_folder_for_standard_user
        self.client.force_login(user=self.standard_user)
        self.schema = SchemaGenerator().get_schema(public=True)

        # Load an inspection for testing - ensure file and asset org match
        self.inspection = Inspection.objects.get(uuid="23896fe4-4baa-11ee-be56-0242ac120002")
        self.inspection.asset.organisation = self.standard_user.organisation
        self.inspection.asset.save()
        self.inspection.file.target_org = self.standard_user.organisation
        self.inspection.file.save()
        self.inspection.folder = self.inspection.file.job_tree
        self.inspection.folder.primary_org = self.standard_user.organisation
        self.inspection.folder.save()

    @pytest.fixture
    def expected_frame_keys(self):
        return [
            "id",
            "inspectionId",
            "imageLocation",
            "imageUrl",
            "frameId",
            "classLabel",
            "classCertainty",
            "chainage",
            "chainageNumber",
            "isHidden",
            "isAccepted",
            "allClassBreakdown",
            "atJoint",
            "atClock",
            "toClock",
            "contDefectStart",
            "contDefectEnd",
            "quantity1Value",
            "quantity1Units",
            "quantity2Value",
            "quantity2Units",
            "remarks",
            "isMatched",
            "parentVideo",
            "pipeTypeSewer",
            "material",
            "defectId",
            "defectClass",
            "defectCode",
            "defectStrScore",
            "defectSerScore",
            "defectScoreSeverity",
            "defectScoreIsShown",
            "timeReference",
        ]

    def test_get_all_inspections_as_standard_user(self, inspection_settings):
        """
        Test that a product owner can get a list of inspections
        """
        # ensure there is a us inspection creation
        folder = JobsTree.objects.filter(standard_key=3).first()
        factory.create_bulk_inspections(
            target_org=self.standard_user.organisation,
            folder=folder,
        )

        response = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.standard_user.organisation_id)
        )

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        assert isinstance(response_data["results"], list)

        # check standard is a standard enum
        for inspection in response_data["results"]:
            assert inspection["standard"] in [standard.value for standard in StandardEnum]

    def test_get_inspection_by_id_as_standard_user(self, inspection_settings):
        """
        Test that a product owner can get an inspection by id

        """

        response = self.client.get(path=inspection_settings.inspection_url.format(inspection_uuid=self.inspection.uuid))
        assert response.status_code == status.HTTP_200_OK

    def test_get_inspection_with_missing_folder(self, inspection_settings):
        self.inspection.folder = None
        self.inspection.save()
        response = self.client.get(path=inspection_settings.inspection_url.format(inspection_uuid=self.inspection.uuid))

        assert response.status_code == status.HTTP_200_OK
        resp = response.json()
        assert resp["folder"] is None
        assert resp["inspectionId"] == str(self.inspection.uuid)

    def test_list_inspections_order_by_diameter(self, inspection_settings: InspectionSettings):
        """
        Test that inspections can be listed and ordered by diameter
        """
        standard = Standard.objects.get(name="MSCC5")
        a1 = Asset.objects.create(organisation=self.standard_user.organisation)
        i1 = Inspection.objects.create(asset=a1)
        m1 = MapPointList.objects.create(inspection=i1, standard_key=standard)
        a1.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="HeightDiameter", standard=standard),
            value="100mm",
            original_value="100mm",
        )
        i1.legacy_id = m1.id
        i1.save()
        # Inspection.objects.create(asset=a1)

        a2 = Asset.objects.create(organisation=self.standard_user.organisation)
        i2 = Inspection.objects.create(asset=a2)
        m2 = MapPointList.objects.create(inspection=i2, standard_key=standard)
        a2.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="HeightDiameter", standard=standard),
            value="200mm",
            original_value="200mm",
        )
        i2.legacy_id = m2.id
        i2.save()
        # Inspection.objects.create(asset=a2)

        a3 = Asset.objects.create(organisation=self.standard_user.organisation)
        i3 = Inspection.objects.create(asset=a3)
        m3 = MapPointList.objects.create(inspection=i3, standard_key=standard)
        a3.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="HeightDiameter", standard=standard),
            value="50mm",
            original_value="50mm",
        )
        i3.legacy_id = m3.id
        i3.save()
        # Inspection.objects.create(asset=a3)

        res = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.standard_user.organisation_id)
            + "&ordering=diameter",
        )
        assert res.status_code == status.HTTP_200_OK

        data = res.json()
        assert len(data["results"]) == 4, "1 from fixture and 3 created here"

        assert data["results"][0]["diameter"] == 50
        assert data["results"][1]["diameter"] == 100
        assert data["results"][2]["diameter"] == 200
        assert data["results"][3]["diameter"] == 750

    def test_list_inspections_order_by_diameter_blank(self, inspection_settings: InspectionSettings):
        standard = Standard.objects.get(name="MSCC5")

        a1 = Asset.objects.create(organisation=self.standard_user.organisation)
        i1 = Inspection.objects.create(asset=a1)
        m1 = MapPointList.objects.create(inspection=i1, standard_key=standard)
        i1.legacy_id = m1.id
        i1.save()

        a1.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="HeightDiameter", standard=standard),
            value="",
            original_value="",
        )

        a2 = Asset.objects.create(organisation=self.standard_user.organisation)
        i2 = Inspection.objects.create(asset=a2)
        m2 = MapPointList.objects.create(inspection=i2, standard_key=standard)
        i2.legacy_id = m2.id
        i2.save()

        a2.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="HeightDiameter", standard=standard),
            value="",
            original_value="",
        )

        a3 = Asset.objects.create(organisation=self.standard_user.organisation)
        i3 = Inspection.objects.create(asset=a3)
        m3 = MapPointList.objects.create(inspection=i3, standard_key=standard)
        i3.legacy_id = m3.id
        i3.save()

        a3.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="HeightDiameter", standard=standard),
            value="50",
            original_value="50",
        )

        res = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.standard_user.organisation_id)
            + "&ordering=diameter",
        )
        assert res.status_code == status.HTTP_200_OK

        data = res.json()
        assert len(data["results"]) == 4, "1 from fixture and 3 created here"
        assert data["results"][0]["diameter"] is None
        assert data["results"][1]["diameter"] is None
        assert data["results"][2]["diameter"] == 50
        assert data["results"][3]["diameter"] == 750

    def test_list_inspections_order_by_chainage_blank(self, inspection_settings: InspectionSettings):
        standard = Standard.objects.get(name="MSCC5")
        a1 = Asset.objects.create(organisation=self.standard_user.organisation)
        i1 = Inspection.objects.create(asset=a1)
        m1 = MapPointList.objects.create(inspection=i1, standard_key=standard)
        a1.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="LengthSurveyed", standard=standard),
            value="",
            original_value="",
        )
        i1.legacy_id = m1.id
        i1.save()
        # Inspection.objects.create(asset=a1)

        a2 = Asset.objects.create(organisation=self.standard_user.organisation)
        i2 = Inspection.objects.create(asset=a2)
        m2 = MapPointList.objects.create(inspection=i2, standard_key=standard)
        a2.assetvalue_set.create(
            standard_header=StandardHeader.objects.get(header__name="LengthSurveyed", standard=standard),
            value="123",
            original_value="123",
        )
        i2.legacy_id = m2.id
        i2.save()
        # Inspection.objects.create(asset=a2)

        res = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.standard_user.organisation_id)
            + "&ordering=chainage",
        )
        assert res.status_code == status.HTTP_200_OK

        data = res.json()
        assert len(data["results"]) == 3
        assert data["results"][0]["chainage"] == 0

    def test_list_inspections_filter_asset_id_by_nonexistent(self, inspection_settings: InspectionSettings):
        """
        Filter by an asset ID that doesn't exist
        """
        res = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.standard_user.organisation_id)
            + "&asset_id=some-nonexistent-id",
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert len(data["results"]) == 0

    def test_list_inspections_filter_asset_id(self, inspection_settings: InspectionSettings):
        """
        Filter by asset ID
        """
        asset = factory.create_assets(org=self.standard_user.organisation)[0]
        inspections = factory.create_bulk_inspections(n=3, asset=asset)
        asset_id = asset.assetvalue_set.get(standard_header__header__name="AssetID")
        asset_id.value = "some-unique-asset-id"
        asset_id.save()

        res = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=self.standard_user.organisation_id)
            + f"&asset_id={asset_id.value}&use_header_names=true",
        )

        assert res.status_code == status.HTTP_200_OK
        data = res.json()

        assert len(data["results"]) == 3, "The inspection created by fixture should not be present"

        expected_ids = {str(inspection["inspection_id"]) for inspection in inspections}
        actual_ids = {inspection["uuid"] for inspection in data["results"]}
        assert expected_ids == actual_ids

    def test_update_inspection_as_product_owner(self):
        """
        Test that a product owner can get an inspection filter record
        """
        pass

    def test_update_inspection_as_standard_user(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user can update an inspection record.
        """
        response = self.client.get(path=inspection_settings.inspection_url.format(inspection_uuid=self.inspection.uuid))
        assert response.status_code == status.HTTP_200_OK

        inspection_obj = response.json()
        assert self.inspection.inspectionvalue_set.count() == 5
        assert inspection_obj["direction"] == "Downstream"

        # patch asset value "Direction"
        resp = self.client.patch(
            path=inspection_settings.inspection_url.format(inspection_uuid=self.inspection.uuid),
            data={"direction": "Upstream"},
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_200_OK
        resp_data = resp.json()

        assert resp_data["direction"] == "Upstream"

        # check updates are visible in fetch list data
        organisation = self.inspection.asset.organisation.id
        response = self.client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=organisation),
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.data
        assert "results" in response_data
        inspection_list = response_data["results"]

        updated_inspection = None
        for inspection in inspection_list:
            if inspection["inspection_id"] == self.inspection.uuid:
                updated_inspection = inspection
                assert inspection["direction"] == "Upstream"

        assert updated_inspection is not None

    def test_update_inspection_asset_as_standard_user(
        self, inspection_settings: InspectionSettings, asset_settings: AssetSettings
    ):
        """
        Test that an inspection can be associated with a different asset
        """
        # get an inspection
        inspection = self.inspection
        asset = factory.create_assets(org=self.standard_user.organisation, standard=inspection.folder.standard_key)
        assert len(asset) == 1
        inspection.asset = asset[0]
        inspection.save()

        # check the asset fields
        assert inspection.asset is not None
        asset_values = inspection.asset.get_asset_values().values_list("standard_header__header__name", "value")
        assert len(asset_values) > 0

        # create a new asset
        new_asset = {
            "organisation": inspection.asset.organisation.id,
            "type": "pipe",
            "standard": inspection.folder.standard_key.id,
            "AssetID": "NEW ASSET ID",
            "UpstreamNode": "New upstream node",
            "DownstreamNode": "New downstream node",
            "HeightDiameter": 100,
            "Material": "clay",
            "LocationStreet": "St",
            "LocationTown": "Town",
        }
        response = self.client.post(path=asset_settings.asset_list_url, data=new_asset, content_type="application/json")
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert "uuid" in response_data
        asset_uuid = response_data["uuid"]

        # patch the new asset uuid to inspection
        response = self.client.patch(
            path=inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid) + "?use_header_names=true",
            data={"asset": asset_uuid},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # check inspection fields match new asset fields
        assert "uuid" in response_data["asset"]
        assert response_data["asset"]["uuid"] == asset_uuid

        for field in new_asset:
            if field in ["organisation", "type"]:
                continue
            assert new_asset[field] == response_data["asset"][field]

        # check mpl record was synced
        legacy_id = response_data["legacyId"]
        legacy_record = MapPointList.objects.get(id=legacy_id)
        assert legacy_record

        asset_values = AssetValue.objects.filter(asset=asset_uuid).prefetch_related("standard_header__header")

        for value in asset_values:
            header = value.standard_header.header

            if header.mapped_mpl_field:
                if header.mapped_mpl_field == "name":
                    assert response_data["name"] == getattr(legacy_record, header.mapped_mpl_field)
                    continue

                assert value.value == getattr(legacy_record, header.mapped_mpl_field)

    def test_update_inspection_asset_different_standard(self, inspection_settings: InspectionSettings):
        """
        Test that a user cannot update an inspection asset with a different standard
        """
        inspection = self.inspection
        asset = factory.create_assets(org=self.standard_user.organisation, standard=inspection.folder.standard_key)
        assert len(asset) == 1
        inspection.asset = asset[0]
        inspection.save()

        inspection_standard = inspection.folder.standard_key
        asset_standard = inspection.asset.standard
        assert inspection_standard.id == 1
        assert asset_standard == inspection_standard.id

        # create a new asset with a different standard
        standard = Standard.objects.filter(id=2).first()
        new_asset = factory.create_assets(org=self.standard_user.organisation, standard=standard)
        assert len(new_asset) == 1

        response = self.client.patch(
            path=inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid),
            data={"asset": new_asset[0].uuid},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == ["Asset standard must match the Inspection Standard"]

    def test_update_inspection_blank_inspection_notes(self, inspection_settings: InspectionSettings):
        inspection = self.inspection

        response = self.client.patch(
            path=inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid),
            data={"inspection_notes": ""},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

    def test_update_inspection_status(self, inspection_settings: InspectionSettings):
        """
        Test inspection status can be updated
        """
        inspection = self.inspection
        assert inspection.status == "Uploaded"

        response = self.client.patch(
            path=inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid),
            data={"status": "Reviewed"},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "status" in response_data
        assert response_data["status"] == "Reviewed"

        # check mpl synced
        legacy_record = MapPointList.objects.get(inspection_id=inspection.uuid)
        assert legacy_record.status == "Reviewed"

        # check reviewed by
        assert legacy_record.reviewed_by is not None

        # confirm repair recommendation was generated
        rr = RepairRecommendation.objects.filter(target_id=legacy_record.id).first()
        assert rr is not None

    def test_update_inspection_chainage(self, inspection_settings: InspectionSettings):
        """
        Test the chainage can be updated and the gradings are re-calculated
        """
        inspection = self.inspection
        response = self.client.get(
            path=inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid),
        )

        assert response.status_code == status.HTTP_200_OK
        inspection_obj = response.json()
        assert inspection_obj["chainage"] == 15.18

        response = self.client.patch(
            path=inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid),
            data={"chainage": 100},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "chainage" in response_data
        assert response_data["chainage"] == 100

        # check mpl synced
        legacy_record = MapPointList.objects.get(inspection_id=inspection.uuid)
        assert legacy_record.chainage == 100
        assert legacy_record.service_condition_rating == response_data["serviceConditionRating"]
        assert legacy_record.condition_rating == response_data["conditionRating"]

    def test_update_inspection_by_header_names(self, inspection_settings: InspectionSettings):
        """
        Test that an inspection can be updated by header names
        """
        inspection = self.inspection
        n_vals_before = inspection.inspectionvalue_set.count()

        response = self.client.patch(
            path=inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid) + "?use_header_names=true",
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": date(2025, 1, 1),
            },
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        inspection.refresh_from_db()
        assert inspection.inspectionvalue_set.count() == n_vals_before, "No new values should be created"
        assert inspection.inspectionvalue_set.get(standard_header__header__name="Direction").value == "U"
        assert inspection.inspectionvalue_set.get(standard_header__header__name="LengthSurveyed").value == "123.45"
        assert inspection.inspectionvalue_set.get(standard_header__header__name="Date").value == "2025-01-01"

        resp_data = response.json()
        assert resp_data["Direction"] == "Upstream", "Response keys should also match header names"
        assert resp_data["LengthSurveyed"] == 123.45, "Response keys should also match header names"
        assert resp_data["Date"] == "2025-01-01", "Response keys should also match header names"

    def test_get_inspection_filter_as_new_user(
        self, inspection_settings: InspectionSettings, user_settings: UserSettings, asset_owner_org
    ):
        """
        Test that a standard user can get an inspection filter record
        """
        self.client.force_login(user=self.product_owner)
        data = {
            "email": "<EMAIL>",
            "firstName": "test",
            "group": 2,
            "isActive": False,
            "isAdmin": False,
            "isMember": True,
            "isStaff": False,
            "lastName": "register",
            "password": "^$7utW5Dx8Z!M3yKTA%a#Hgi",
            "organisation_id": asset_owner_org.id,
        }

        response = self.client.post(
            user_settings.user_base_url,
            data=data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        user = CustomUser.objects.get(email=data["email"])
        user.is_active = True
        user.save()

        self.client.force_login(user=user)

        response = self.client.get(
            path=f"{inspection_settings.get_inspection_filter_url}?organisation={user.organisation.id}",
        )

        assert response.status_code == status.HTTP_200_OK

    def test_create_inspection_as_standard_user(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user can create an inspection
        """

        asset = Asset.objects.create(organisation=self.standard_user.organisation)
        file = FileList.objects.create(
            filename="test_file",
            target_org=self.standard_user.organisation,
            job_tree=self.root_folder,
            upload_org=self.standard_user.organisation,
            uploaded_by=self.standard_user,
        )

        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, file.id, self.root_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_201_CREATED

        resp_data = resp.json()
        inspection_id = resp_data["uuid"]

        inspection = Inspection.objects.get(uuid=inspection_id)
        assert inspection.asset == asset
        assert inspection.folder == self.root_folder
        assert inspection.file == file
        assert inspection.status == StatusEnum.UPLOADED
        assert inspection.legacy_id == f"{file.target_org.id}-00000002"

        values = {val.standard_header.header.name: val.value for val in inspection.inspectionvalue_set.all()}
        assert values == {
            "Direction": "Upstream",
            "LengthSurveyed": "123.45",
            "Date": "2025-01-01",
            "GeneralRemarks": "some notes...",
            "WorkOrder": "",
        }

    def test_create_inspection_on_nonexistent_asset(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user cannot create an inspection on a non-existent asset
        """

        fake_uuid = uuid.UUID("74f20a00-22b6-4e08-9a2c-070ad0c706d4")
        file = FileList.objects.create(
            filename="test_file", target_org=self.standard_user.organisation, job_tree=self.root_folder
        )
        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(fake_uuid, file.id, self.root_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_create_inspection_where_file_does_not_exist(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user cannot create an inspection where the file does not exist
        """

        asset = Asset.objects.create(organisation=self.standard_user.organisation)
        fake_id = 12345
        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, fake_id, self.root_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_create_inspection_where_folder_does_not_exist(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user cannot create an inspection where the folder does not exist
        """

        asset = Asset.objects.create(organisation=self.standard_user.organisation)
        file = FileList.objects.create(
            filename="test_file", target_org=self.standard_user.organisation, job_tree=self.root_folder
        )
        fake_id = 12345
        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, file.id, fake_id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_create_inspection_forbidden_file(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user cannot create an inspection where the file does not belong to the organisation
        """

        asset = Asset.objects.create(organisation=self.standard_user.organisation)
        another_org = Organisations.objects.create(full_name="another_test_org", standard_key=Standard.objects.first())
        file = FileList.objects.create(filename="test_file", target_org=another_org)
        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, file.id, self.root_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_create_inspection_forbidden_asset(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user cannot create an inspection where the asset does not belong to the organisation
        """

        another_org = Organisations.objects.create(full_name="another_test_org", standard_key=Standard.objects.first())
        asset = Asset.objects.create(organisation=another_org)
        file = FileList.objects.create(
            filename="test_file", target_org=self.standard_user.organisation, job_tree=self.root_folder
        )
        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, file.id, self.root_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_create_inspection_forbidden_folder(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user cannot create an inspection where the folder does not belong to the organisation
        """

        asset = Asset.objects.create(organisation=self.standard_user.organisation)
        file = FileList.objects.create(
            filename="test_file", target_org=self.standard_user.organisation, job_tree=self.root_folder
        )
        another_org = Organisations.objects.create(full_name="another_test_org", standard_key=Standard.objects.first())
        another_folder = JobsTree.add_root(primary_org=another_org, created_date=dj_timezone.now())
        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, file.id, another_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def test_create_multiple_inspections_on_asset(self, inspection_settings: InspectionSettings):
        """
        Test that a standard user can create multiple inspections on an asset
        """

        asset = Asset.objects.create(organisation=self.standard_user.organisation)
        file1 = FileList.objects.create(
            filename="test_file1",
            target_org=self.standard_user.organisation,
            job_tree=self.root_folder,
            upload_org=self.standard_user.organisation,
            uploaded_by=self.standard_user,
        )
        file2 = FileList.objects.create(
            filename="test_file2",
            target_org=self.standard_user.organisation,
            job_tree=self.root_folder,
            upload_org=self.standard_user.organisation,
            uploaded_by=self.standard_user,
        )

        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, file1.id, self.root_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_201_CREATED

        resp_data = resp.json()
        inspection_id = resp_data["uuid"]

        inspection_1 = Inspection.objects.get(uuid=inspection_id)
        assert inspection_1.asset == asset
        assert inspection_1.folder == self.root_folder
        assert inspection_1.file == file1
        assert inspection_1.status == StatusEnum.UPLOADED

        values = {val.standard_header.header.name: val.value for val in inspection_1.inspectionvalue_set.all()}
        assert values == {
            "Direction": "Upstream",
            "LengthSurveyed": "123.45",
            "Date": "2025-01-01",
            "GeneralRemarks": "some notes...",
            "WorkOrder": "",
        }

        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset.uuid, file2.id, self.root_folder.id),
            data={
                "Direction": "Downstream",
                "LengthSurveyed": 543.21,
                "Date": datetime(2025, 1, 2),
                "GeneralRemarks": "some more notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_201_CREATED
        assert asset.inspection_set.count() == 2

        resp_data = resp.json()
        inspection_id = resp_data["uuid"]

        inspection_2 = Inspection.objects.get(uuid=inspection_id)
        assert inspection_2.asset == asset
        assert inspection_2.folder == self.root_folder
        assert inspection_2.file == file2
        assert inspection_2.status == StatusEnum.UPLOADED

        values = {val.standard_header.header.name: val.value for val in inspection_2.inspectionvalue_set.all()}
        assert values == {
            "Direction": "Downstream",
            "LengthSurveyed": "543.21",
            "Date": "2025-01-02",
            "GeneralRemarks": "some more notes...",
            "WorkOrder": "",
        }

        values = {val.standard_header.header.name: val.value for val in inspection_1.inspectionvalue_set.all()}
        assert values == {
            "Direction": "Upstream",
            "LengthSurveyed": "123.45",
            "Date": "2025-01-01",
            "GeneralRemarks": "some notes...",
            "WorkOrder": "",
        }, "First inspection values should not change"

    def test_create_multiple_inspections_same_org(self, inspection_settings: InspectionSettings):
        asset1 = Asset.objects.create(organisation=self.standard_user.organisation)
        asset2 = Asset.objects.create(organisation=self.standard_user.organisation)
        file1 = FileList.objects.create(
            filename="test_file1",
            target_org=self.standard_user.organisation,
            job_tree=self.root_folder,
            upload_org=self.standard_user.organisation,
            uploaded_by=self.standard_user,
        )
        file2 = FileList.objects.create(
            filename="test_file2",
            target_org=self.standard_user.organisation,
            job_tree=self.root_folder,
            upload_org=self.standard_user.organisation,
            uploaded_by=self.standard_user,
        )

        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset1.uuid, file1.id, self.root_folder.id),
            data={
                "Direction": "Upstream",
                "LengthSurveyed": 123.45,
                "Date": datetime(2025, 1, 1),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_201_CREATED

        resp_data = resp.json()
        inspection_id = resp_data["uuid"]

        org_id = self.standard_user.organisation.id

        inspection_1 = Inspection.objects.get(uuid=inspection_id)
        assert inspection_1.legacy_id == f"{org_id}-00000002"

        last_vapar_id = Inspection.objects.filter(file__target_org=file1.target_org).latest("legacy_id").legacy_id
        assert last_vapar_id == f"{org_id}-00000002"

        resp = self.client.post(
            path=inspection_settings.get_inspections_post_url(asset2.uuid, file2.id, self.root_folder.id),
            data={
                "Direction": "Downstream",
                "LengthSurveyed": 23.45,
                "Date": datetime(2025, 1, 2),
                "GeneralRemarks": "some notes...",
                "WorkOrder": "",
            },
            content_type="application/json",
        )

        assert resp.status_code == status.HTTP_201_CREATED

        resp_data = resp.json()
        inspection_id = resp_data["uuid"]

        last_inspections = Inspection.objects.filter(
            file__target_org=file1.target_org
        )  # .latest("legacy_id").legacy_id
        assert len(last_inspections) == 3

        inspection_2 = Inspection.objects.get(uuid=inspection_id)
        assert inspection_2.legacy_id == f"{org_id}-00000003"

    def test_fetch_frames_list(self, expected_frame_keys, inspection_settings, standard_user):
        """
        Test that a list of frames can be fetched
        """
        client = Client()
        client.force_login(user=standard_user)

        inspection = self.inspection
        response = client.get(path=inspection_settings.get_frames_list_url.format(mpl_id=inspection.legacy_id))

        assert response.status_code == status.HTTP_200_OK
        frames = response.json()
        assert isinstance(frames, list)

        for frame in frames:
            keys = frame.keys()
            assert list(keys) == expected_frame_keys, "Keys do not match expected frame keys"

            assert isinstance(frame["imageUrl"], str)
            assert "mock-sas-token" in frame["imageUrl"], "SAS token should be in the image URL"
            assert frame["imageLocation"] in frame["imageUrl"], "Image location should be in the image URL"

            # None when no defect, "none" when defect doesn't have a matching severity
            assert frame["defectScoreSeverity"] in (None, "none", "low", "medium", "high")

    def test_fetch_global_frames_list(self, expected_frame_keys, inspection_settings, standard_user):
        """
        Test that a list of frames not directly coupled to an inspection can be fetched
        """
        client = Client()
        client.force_login(user=standard_user)

        response = client.get(
            path=inspection_settings.get_frames_global_list_url + f"?parent_video__in={self.inspection.file.id}"
        )

        assert response.status_code == status.HTTP_200_OK
        frames = response.json()["results"]
        for frame in frames:
            keys = frame.keys()
            assert list(keys) == expected_frame_keys, "Keys do not match expected frame keys"

            assert isinstance(frame["imageUrl"], str)
            assert "mock-sas-token" in frame["imageUrl"], "SAS token should be in the image URL"
            assert frame["imageLocation"] in frame["imageUrl"], "Image location should be in the image URL"

    def test_frame_update(self, expected_frame_keys, inspection_settings, standard_user):
        """
        Test that a frame can be updated
        """
        client = Client()
        client.force_login(user=standard_user)

        inspection = self.inspection
        assert inspection.service_grade == 1
        assert inspection.structural_grade == 1

        # get frames
        response = client.get(path=inspection_settings.get_frames_list_url.format(mpl_id=inspection.legacy_id))
        assert response.status_code == status.HTTP_200_OK

        response_frames = response.json()
        assert isinstance(response_frames, list)

        for frame in response_frames:
            keys = frame.keys()
            assert list(keys) == expected_frame_keys, "Keys do not match expected frame keys"

        # make one frame collapse defect
        response = client.patch(
            path=inspection_settings.get_frame_update_defect_url.format(frame_id=response_frames[0]["id"]),
            data={"defectId": 2231},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "inspectionStrGrade" in response_data
        assert response_data["inspectionStrGrade"] == 5

        # check structural grade is high
        inspection.refresh_from_db()
        assert inspection.structural_grade == 5

        # hide the frame causing high structural grade
        response = client.patch(
            path=inspection_settings.get_frame_url.format(frame_id=response_frames[0]["id"]),
            data={"isHidden": True},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        inspection.refresh_from_db()
        assert inspection.structural_grade == 1

        response_data = response.json()
        assert "inspectionStrGrade" in response_data
        assert response_data["inspectionStrGrade"] == 1

        # check mpl syncing
        mpl = MapPointList.objects.get(inspection_id=inspection.uuid)
        assert mpl.condition_rating == 1

        # check cache
        response = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation.id)
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        inspection_list = response_data["results"]

        # Check the cache has been updated with the latest inspection data.
        check_original_inspection_updated = False
        for insp in inspection_list:
            if insp["inspectionId"] == str(inspection.uuid):
                assert insp["serviceConditionRating"] == inspection.service_grade
                assert insp["conditionRating"] == inspection.structural_grade
                check_original_inspection_updated = True

        assert check_original_inspection_updated

    def test_frame_defect_update(self, expected_frame_keys, inspection_settings, standard_user):
        """
        Test that a frames defect can be updated
        """
        client = Client()
        client.force_login(user=standard_user)

        inspection = self.inspection
        assert inspection.service_grade == 1
        assert inspection.structural_grade == 1

        response = client.get(path=inspection_settings.get_frames_list_url.format(mpl_id=inspection.legacy_id))
        assert response.status_code == status.HTTP_200_OK
        frames = response.json()
        assert isinstance(frames, list)

        for frame in frames:
            keys = frame.keys()
            assert list(keys) == expected_frame_keys, "Keys do not match expected frame keys"

        # update 2 frames - 1 to most severe structural and 1 to most severe service
        response = client.patch(
            path=inspection_settings.get_frame_update_defect_url.format(frame_id=frames[0]["id"]),
            data={"defectId": 1619},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        response = client.patch(
            path=inspection_settings.get_frame_update_defect_url.format(frame_id=frames[1]["id"]),
            data={"defectId": 2140},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        inspection = Inspection.objects.filter(uuid=inspection.uuid).first()
        assert inspection
        assert inspection.service_grade == 3
        assert inspection.structural_grade == 5

        # check mpl for syncing
        mpl = MapPointList.objects.filter(id=inspection.legacy_id).first()
        assert mpl
        assert mpl.service_condition_rating == 3
        assert mpl.condition_rating == 5

        # check inspection list for caching
        response = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation.id)
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "results" in response_data
        inspection_list = response_data["results"]

        # Check the cache has been updated with the latest inspection data.
        check_original_inspection_updated = False
        for insp in inspection_list:
            if insp["inspectionId"] == str(inspection.uuid):
                assert insp["serviceConditionRating"] == inspection.service_grade
                assert insp["conditionRating"] == inspection.structural_grade
                check_original_inspection_updated = True

        assert check_original_inspection_updated

    def test_update_frame_chainage(self, inspection_settings: InspectionSettings):
        """
        Test that a frame chainage can be updated
        """
        self.client.force_login(user=self.standard_user)

        inspection = self.inspection

        response = self.client.get(
            path=inspection_settings.get_frames_list_url.format(mpl_id=inspection.legacy_id),
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert isinstance(response_data, list)
        assert len(response_data) > 0

        frame = response_data[0]

        response = self.client.patch(
            path=inspection_settings.get_frame_url.format(frame_id=frame["id"]),
            data={"chainageNumber": 100},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "chainageNumber" in response_data
        assert response_data["chainageNumber"] == 100
        assert "chainage" in response_data
        assert response_data["chainage"] == str(float(100))

    def test_video_frames_create(self, service_user: CustomUser, inspection_settings: InspectionSettings):
        client = Client()
        client.force_login(service_user)

        parent_video = FileList.objects.create(
            filename="test_video_frames_create.mp4",
            target_org=service_user.organisation,
            upload_org=service_user.organisation,
        )
        data_json = [
            {
                "parent_video": parent_video.id,
                "frame_id": 1,
                "chainage_number": 0.1,
                "class_label": "Other",
                "class_certainty": 0.99,
                "all_class_breakdown": "",
            }
        ]

        response = client.post(
            path=inspection_settings.get_post_video_frames_url(video_id=parent_video.id),
            data=data_json,
            content_type="application/json",
        )

        assert response.status_code == 201
        validate(instance=response.json(), schema=self.schema)

        created_frames = VideoFrames.objects.filter(parent_video=parent_video)
        assert len(created_frames) == 1

        input_data = data_json[0]
        assert created_frames.first().frame_id == input_data["frame_id"]
        assert created_frames.first().chainage_number == input_data["chainage_number"]
        assert created_frames.first().class_label == input_data["class_label"]
        assert created_frames.first().class_certainty == round(Decimal(input_data["class_certainty"]), 2)
        assert created_frames.first().all_class_breakdown == input_data["all_class_breakdown"]

    def test_video_frames_create_bad_data(self, service_user: CustomUser, inspection_settings: InspectionSettings):
        client = Client()
        client.force_login(service_user)

        parent_video = FileList.objects.create(
            filename="test_video_frames_create.mp4",
            target_org=service_user.organisation,
            upload_org=service_user.organisation,
        )
        data_json = [
            {
                "parent_video": parent_video.id,
                "frame_id": "9",
                "chainage_number": 10,
                "class_label": None,
                "class_certainty": 1.1,
                "all_class_breakdown": None,
            }
        ]

        response = client.post(
            path=inspection_settings.get_post_video_frames_url(video_id=parent_video.id),
            data=data_json,
            content_type="application/json",
        )

        assert response.status_code == 400

    def test_video_frames_patch_multiple(self, service_user: CustomUser, inspection_settings: InspectionSettings):
        client = Client()
        client.force_login(service_user)

        parent_video = FileList.objects.create(
            filename="test_video_frames_create.mp4",
            target_org=service_user.organisation,
            upload_org=service_user.organisation,
        )
        vf1 = parent_video.videoframes_set.create(
            frame_id=1,
            image_location="videoframefiles/1.jpg",
            chainage_number=0.1,
            class_label="Other",
            class_certainty=0.99,
            all_class_breakdown="",
        )
        vf2 = parent_video.videoframes_set.create(
            frame_id=2,
            image_location="videoframefiles/2.jpg",
            chainage_number=2.0,
            class_label="Other",
            class_certainty=0.99,
            all_class_breakdown="",
        )
        vf3 = parent_video.videoframes_set.create(
            frame_id=3,
            image_location="videoframefiles/3.jpg",
            chainage_number=3.5,
            class_label="Other",
            class_certainty=0.99,
            all_class_breakdown="",
        )

        data_json = [
            {
                "id": vf1.id,
                "imageLocation": "videoframefiles/new_1.jpg",
            },
            {
                "id": vf3.id,
                "imageLocation": "videoframefiles/new_3.jpg",
            },
        ]

        response = client.patch(
            path=inspection_settings.get_post_video_frames_url(video_id=parent_video.id),
            data=data_json,
            content_type="application/json",
        )
        assert response.status_code == 200

        vf1.refresh_from_db()
        vf2.refresh_from_db()
        vf3.refresh_from_db()

        assert vf1.image_location == "videoframefiles/new_1.jpg"
        assert vf2.image_location == "videoframefiles/2.jpg", "Should not have changed"
        assert vf3.image_location == "videoframefiles/new_3.jpg"


def test_recent_uploads_default_ordering(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="3",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_uploaded_files_url)
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 3

    assert files[0]["filename"] == "3"
    assert files[1]["filename"] == "2"
    assert files[2]["filename"] == "1"


def test_recent_uploads_asc_ordering(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="3",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_uploaded_files_url + "&ordering=upload_completed_time")
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 3

    assert files[0]["filename"] == "1"
    assert files[1]["filename"] == "2"
    assert files[2]["filename"] == "3"


def test_recent_uploads_order_by_filename(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    FileList.objects.create(
        filename="abc",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="ghi",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="def",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_uploaded_files_url + "&ordering=filename")
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 3

    assert files[0]["filename"] == "abc"
    assert files[1]["filename"] == "def"
    assert files[2]["filename"] == "ghi"


def test_recent_uploads_as_contractor(
    asset_owner_org, linked_contractor_org, linked_contractor_user, inspection_settings
):
    client = Client()
    target_org = asset_owner_org

    FileList.objects.create(
        filename="1",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="3",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="2",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=linked_contractor_user)
    res = client.get(inspection_settings.list_uploaded_files_url + f"&target_org_id={target_org.id}")
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 3

    assert files[0]["filename"] == "3"
    assert files[1]["filename"] == "2"
    assert files[2]["filename"] == "1"


def test_recent_uploads_nonexistent_org(
    asset_owner_org, linked_contractor_org, linked_contractor_user, inspection_settings
):
    client = Client()
    target_org = asset_owner_org

    FileList.objects.create(
        filename="1",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="3",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="2",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=linked_contractor_user)
    res = client.get(inspection_settings.list_uploaded_files_url + "&target_org_id=999999")
    assert res.status_code == status.HTTP_404_NOT_FOUND


def test_recent_uploads_wrong_org(linked_contractor_org, linked_contractor_user, inspection_settings):
    client = Client()
    target_org = Organisations.objects.create(
        full_name="another_org",
        standard_key=Standard.objects.first(),
    )

    FileList.objects.create(
        filename="1",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="3",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="2",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=linked_contractor_user)
    res = client.get(inspection_settings.list_uploaded_files_url + f"&target_org_id={target_org.id}")
    assert res.status_code == status.HTTP_403_FORBIDDEN


def test_recent_uploads_incomplete_excluded(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=False,
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_uploaded_files_url)
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 1

    assert files[0]["filename"] == "1"


def test_recent_uploads_upload_only_user_only_sees_own_uploads(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    standard_user.user_level = UserLevelEnum.UPLOAD_ONLY
    standard_user.save()

    FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        uploaded_by=standard_user,
    )
    FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_uploaded_files_url)
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 1
    assert files[0]["filename"] == "1"


def test_file_list_get_all_from_org(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=False,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_files_url)
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 2

    assert files[0]["filename"] == "2"
    assert files[1]["filename"] == "1"


def test_file_list_search_by_name(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    FileList.objects.create(
        filename="abc",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="def",
        target_org=org,
        upload_completed=False,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_files_url + "?filename=abc")
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 1

    assert files[0]["filename"] == "abc"


def test_file_list_search_by_name_for_nonexistent(asset_owner_org, standard_user, inspection_settings):
    client = Client()
    org = asset_owner_org

    FileList.objects.create(
        filename="abc",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="def",
        target_org=org,
        upload_completed=False,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_files_url + "?filename=xyz")
    assert res.status_code == status.HTTP_200_OK
    res_data = res.json()
    assert res_data["count"] == 0
    assert len(res_data["results"]) == 0


def test_file_list_contractor_search_by_name_in_target_org(
    asset_owner_org, linked_contractor_org, linked_contractor_user, inspection_settings
):
    client = Client()
    target_org = asset_owner_org

    FileList.objects.create(
        filename="abc",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="def",
        target_org=target_org,
        upload_org=linked_contractor_org,
        upload_completed=False,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 3, tzinfo=timezone.utc),
    )
    FileList.objects.create(
        filename="abc",
        target_org=linked_contractor_org,
        upload_org=linked_contractor_org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )

    client.force_login(user=linked_contractor_user)
    res = client.get(inspection_settings.list_files_url + f"?filename=abc&target_org_id={target_org.id}")
    assert res.status_code == status.HTTP_200_OK
    files = res.json()["results"]
    assert len(files) == 1

    assert files[0]["filename"] == "abc"


def test_view_disabled(
    standard_user,
    asset_owner_org,
    linked_contractor_inspection,
    inspection_settings,
):
    """
    Test that view is disabled for an asset owner if:
    - the contractor has uploaded and has uploaded status
    - the file is missing and status is PLANNED
    """
    client = Client()
    client.force_login(user=standard_user)
    assert standard_user.organisation.id == asset_owner_org.id

    linked_contractor_inspection = linked_contractor_inspection
    asset = Asset.objects.get(uuid=linked_contractor_inspection["asset"]["uuid"])
    inspection_without_file = Inspection.objects.create(asset=asset, file=None, status=StatusEnum.PLANNED)

    # fetch inspection list
    response = client.get(
        path=inspection_settings.inspection_list_url.format(organisation_id=asset_owner_org.id),
    )
    assert response.status_code == status.HTTP_200_OK
    assert "count" in response.data
    assert response.data["count"] > 0
    assert "results" in response.data

    inspections = response.data["results"]
    assert isinstance(inspections, list)

    for inspection in inspections:
        if inspection["inspection_id"] in [
            str(linked_contractor_inspection["inspection_id"]),
            inspection_without_file.uuid,
        ]:
            assert "view_disabled" in inspection
            assert "disabled" in inspection["view_disabled"]
            assert inspection["view_disabled"]["disabled"]
            break


def test_view_enabled(asset_owner_org, linked_contractor_user, linked_contractor_inspection, inspection_settings):
    """
    Test that an inspection can be viewed
    """
    client = Client()
    client.force_login(user=linked_contractor_user)

    response = client.get(
        path=inspection_settings.inspection_list_url.format(organisation_id=asset_owner_org.id),
    )
    assert response.status_code == status.HTTP_200_OK
    assert "count" in response.data
    assert response.data["count"] > 0
    assert "results" in response.data

    inspections = response.data["results"]
    assert isinstance(inspections, list)

    for inspection in inspections:
        if inspection["inspection_id"] == linked_contractor_inspection["inspection_id"]:
            assert "view_disabled" in inspection
            assert "disabled" in inspection["view_disabled"]
            assert not inspection["view_disabled"]["disabled"]
            break


def test_bulk_status_update(inspection_settings, standard_user):
    """
    Test that inspection status can be updated in bulk
    """
    client = Client()
    client.force_login(user=standard_user)
    factory.create_bulk_inspections(target_org=standard_user.organisation, n=3)
    inspections = Inspection.objects.filter(
        Q(file__target_org=standard_user.organisation) | Q(file__upload_org=standard_user.organisation)
    )
    assert inspections is not None
    assert len(inspections) > 0

    inspection_ids = list(inspections.values_list("uuid", flat=True))
    inspection_ids_str = list(map(str, inspection_ids))
    updated_status = "Repair Plan"

    response = client.post(
        path=inspection_settings.get_inspection_bulk_status_update_url,
        data={"inspection_ids": inspection_ids_str, "status": updated_status},
        content_type="application/json",
    )

    assert response.status_code == status.HTTP_200_OK

    # MPL sync check
    mappoints = MapPointList.objects.filter(inspection__in=inspection_ids)
    for mappoint in mappoints:
        assert mappoint.status == updated_status
        assert mappoint.inspection.status == updated_status

    # check inspection list cache
    response = client.get(
        path=inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation.id)
    )

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert "results" in response_data
    inspections = response_data["results"]
    for inspection in inspections:
        if inspection["inspectionId"] in inspection_ids_str:
            assert inspection["status"] == updated_status


def test_bulk_folder_move(
    folder,
    inspection_settings,
    standard_user,
):
    """
    Test that inspections can be moved to another folder in bulk
    """
    client = Client()
    client.force_login(user=standard_user)
    inspections = factory.create_bulk_inspections(target_org=standard_user.organisation, n=3)
    inspection_legacy_ids = [inspection["id"] for inspection in inspections]
    inspection_ids = [inspection["inspection_id"] for inspection in inspections]

    response = client.patch(
        path=inspection_settings.get_inspection_bulk_move_folder_url.format(folder_id=folder.id),
        data={"inspection_ids": inspection_ids},
        content_type="application/json",
    )

    assert response.status_code == status.HTTP_200_OK

    inspections_folders = Inspection.objects.filter(uuid__in=inspection_ids).values_list("folder", flat=True)
    assert all(insp_folder == folder.id for insp_folder in inspections_folders)

    # check syncing
    mpls_folders = MapPointList.objects.filter(id__in=inspection_legacy_ids).values_list(
        "associated_file__job_tree", flat=True
    )
    assert all(mpl_folder == folder.id for mpl_folder in mpls_folders)

    # check cache
    response = client.get(
        path=inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation.id)
    )
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert "results" in response_data

    inspections = response_data["results"]
    assert inspections

    for inspection in inspections:
        if inspection["inspectionId"] in inspection_ids:
            assert "folder" in inspection
            assert inspection["folder"]["id"] == folder.id


def test_bulk_delete_inspections(inspection_settings, standard_user):
    """
    Test that inspections can be deleted in bulk
    """
    client = Client()
    client.force_login(user=standard_user)
    inspections = factory.create_bulk_inspections(target_org=standard_user.organisation, n=3)
    inspection_mpl_ids = [inspection["id"] for inspection in inspections]
    inspection_ids = [inspection["inspection_id"] for inspection in inspections]

    # delete inspections
    response = client.delete(
        path=inspection_settings.get_inspection_bulk_delete_url,
        data={"inspection_ids": inspection_ids},
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_200_OK

    inspections = Inspection.objects.filter(uuid__in=inspection_ids, file__hidden=False)
    assert len(inspections) == 0

    # check syncing
    mpls = MapPointList.objects.filter(id__in=inspection_mpl_ids, associated_file__hidden=False)
    assert len(mpls) == 0

    # check inspection list cache
    response = client.get(
        path=inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation.id)
    )
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert "results" in response_data
    inspections = response_data["results"]

    for inspection in inspections:
        assert inspection["inspectionId"] not in inspection_ids


def test_update_gradings(standard_user):
    """
    Test update gradings will work when Inspection is not passed.
    """
    inspection_uuid = factory.create_bulk_inspections(target_org=standard_user.organisation, n=1)[0]["inspection_id"]
    inspection = Inspection.objects.filter(uuid=inspection_uuid).first()
    assert inspection.service_grade == 1
    assert inspection.structural_grade == 1

    inspection_with_file = Inspection.objects.get(file=inspection.file)
    assert inspection_with_file == inspection

    scores = update_gradings(inspection)
    inspection.refresh_from_db()
    assert inspection.structural_grade == scores[0]
    assert inspection.service_grade == scores[1]


@pytest.fixture(scope="function")
def inspections_for_ordering(standard_user: CustomUser) -> list[Inspection]:
    standard = Standard.objects.get(name="WSA-05 2013")
    sh_diameter = StandardHeader.objects.get(header__name="HeightDiameter", standard=standard, header__type="asset")
    sh_chainage = StandardHeader.objects.get(
        header__name="LengthSurveyed", standard=standard, header__type="inspection"
    )
    sh_upstream_node = StandardHeader.objects.get(header__name="UpstreamNode", standard=standard, header__type="asset")
    sh_downstream_node = StandardHeader.objects.get(
        header__name="DownstreamNode", standard=standard, header__type="asset"
    )
    sh_material = StandardHeader.objects.get(header__name="Material", standard=standard, header__type="asset")
    sh_date_captured = StandardHeader.objects.get(header__name="Date", standard=standard, header__type="inspection")
    sh_direction = StandardHeader.objects.get(header__name="Direction", standard=standard, header__type="inspection")

    a1 = Asset.objects.create(organisation=standard_user.organisation)
    i1 = Inspection.objects.create(asset=a1, service_grade=3, structural_grade=3)
    m1 = MapPointList.objects.create(inspection=i1, standard_key=standard)
    i1.legacy_id = m1.id
    i1.save()

    a1.assetvalue_set.create(
        standard_header=sh_diameter,
        value="100mm",
        original_value="100mm",
    )
    a1.assetvalue_set.create(
        standard_header=sh_chainage,
        value="100",
        original_value="100",
    )
    a1.assetvalue_set.create(
        standard_header=sh_upstream_node,
        value="A1 Upstream",
        original_value="A1 Upstream",
    )
    a1.assetvalue_set.create(
        standard_header=sh_downstream_node,
        value="A1 Downstream",
        original_value="A1 Downstream",
    )
    a1.assetvalue_set.create(
        standard_header=sh_upstream_node,
        value="A2 Upstream",
        original_value="A2 Upstream",
    )
    a1.assetvalue_set.create(
        standard_header=sh_material,
        value="Vitrified Clay",
        original_value="Vitrified Clay",
    )
    a1.assetvalue_set.create(
        standard_header=sh_date_captured,
        value=datetime(2025, 1, 1).isoformat(),
        original_value=datetime(2025, 1, 1).isoformat(),
    )
    i1.inspectionvalue_set.create(
        standard_header=sh_direction,
        value="Upstream",
        original_value="Upstream",
    )
    i1.inspectionvalue_set.create(
        standard_header=sh_date_captured,
        value=datetime(2025, 1, 1).isoformat(),
        original_value=datetime(2025, 1, 1).isoformat(),
    )

    # These 2 inspections will not have any inspection or asset values populated

    a2 = Asset.objects.create(organisation=standard_user.organisation)
    i2 = Inspection.objects.create(asset=a2)
    m2 = MapPointList.objects.create(inspection=i2, standard_key=standard)
    i2.legacy_id = m2.id
    i2.save()

    a3 = Asset.objects.create(organisation=standard_user.organisation)
    i3 = Inspection.objects.create(asset=a3)
    m3 = MapPointList.objects.create(inspection=i3, standard_key=standard)
    i3.legacy_id = m3.id
    i3.save()

    a4 = Asset.objects.create(organisation=standard_user.organisation)
    i4 = Inspection.objects.create(asset=a4)
    i4.legacy_id = "133-0004"
    i4.save()

    return [i1, i2, i3, i4]


def test_list_inspections_search(standard_user: CustomUser, inspection_settings: InspectionSettings):
    client = Client()
    client.force_login(user=standard_user)

    standard = Standard.objects.get(name="WSA-05 2013")
    sh_upstream_node = StandardHeader.objects.get(header__name="UpstreamNode", standard=standard, header__type="asset")
    sh_downstream_node = StandardHeader.objects.get(
        header__name="DownstreamNode", standard=standard, header__type="asset"
    )

    a1 = Asset.objects.create(organisation=standard_user.organisation)
    a1.assetvalue_set.create(
        standard_header=sh_upstream_node,
        value="A1 Upstream",
        original_value="A1 Upstream",
    )
    a1.assetvalue_set.create(
        standard_header=sh_downstream_node,
        value="A1 Downstream",
        original_value="A1 Downstream",
    )
    i1 = Inspection.objects.create(asset=a1)
    i1.legacy_id = 1000
    i1.save()

    a2 = Asset.objects.create(organisation=standard_user.organisation)
    a2.assetvalue_set.create(
        standard_header=sh_upstream_node,
        value="A2 Upstream",
        original_value="A2 Upstream",
    )
    a2.assetvalue_set.create(
        standard_header=sh_downstream_node,
        value="A2 Downstream",
        original_value="A2 Downstream",
    )
    i2 = Inspection.objects.create(asset=a2)
    i2.legacy_id = 2000
    i2.save()

    search_param = "A2"
    res = client.get(
        path=inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation_id)
        + f"&ordering=-id&search={search_param}",
    )
    assert res.status_code == status.HTTP_200_OK
    data = res.json()
    assert data["results"][0]["id"] == "2000"


def test_list_inspections_exclude_null_ids(standard_user: CustomUser, inspection_settings: InspectionSettings):
    client = Client()
    client.force_login(user=standard_user)

    standard = Standard.objects.get(name="WSA-05 2013")

    a1 = Asset.objects.create(organisation=standard_user.organisation)
    i1 = Inspection.objects.create(asset=a1)
    m1 = MapPointList.objects.create(inspection=i1, standard_key=standard)
    i1.legacy_id = m1.id
    i1.save()

    a2 = Asset.objects.create(organisation=standard_user.organisation)
    i2 = Inspection.objects.create(asset=a2)
    # Inspection created without a file object, ID will be null, not returned
    assert i2.legacy_id is None

    a3 = Asset.objects.create(organisation=standard_user.organisation)
    # Inspection created without a file object, ID will be null - but status is 'Planned' so will be returned.
    i3 = Inspection.objects.create(asset=a3)
    i3.status = StatusEnum.PLANNED.value
    i3.save()

    res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation_id))
    data = res.json()
    assert len(data["results"]) == 2
    assert i2.uuid not in [result["inspectionId"] for result in data["results"]]


class TestInspectionValidation:
    def test_validate_erroneous_direction(self):
        i1 = InspectionModel.model_validate({"Direction": "Z"}, context={"standard": StandardEnum.WSA05_2013})
        assert i1.direction == DirectionEnum.UNKNOWN
        i2 = InspectionModel.model_validate({"Direction": "Z"}, context={"standard": StandardEnum.WSA05_2020})
        assert i2.direction == DirectionEnum.UNKNOWN
        i3 = InspectionModel.model_validate({"Direction": "foo"}, context={"standard": StandardEnum.WSA05_2020})
        assert i3.direction == DirectionEnum.UNKNOWN

    def test_validate_erroneous_pipe_type(self):
        i1 = InspectionModel.model_validate({"UseOfDrainSewer": "bar"}, context={"standard": StandardEnum.WSA05_2013})
        assert i1.pipe_type == PipeTypeEnum.SEWER


class TestInspectionListFiltering:
    """
    Test the different ways that GET /inspections2 can be filtered.

    All of these test cases rely on the same 3 inspections configured with different asset and inspection values.
    """

    @pytest.fixture
    def inspections(self, asset_owner_org: Organisations, standard_user: CustomUser):
        now = dj_timezone.now()
        org = asset_owner_org
        root_folder = org.root_folders.first()
        folder_1 = root_folder.add_child(
            job_name="F1",
            pipe_type_sewer=org.sewer_data,
            standard_key=org.standard_key,
            created_date=now,
        )
        folder_2 = root_folder.add_child(
            job_name="F2",
            pipe_type_sewer=org.sewer_data,
            standard_key=org.standard_key,
            created_date=now,
        )

        file_1 = FileList.objects.create(
            filename="test_file1.jpg", target_org=org, upload_org=org, job_tree=folder_1, uploaded_by=standard_user
        )
        file_2 = FileList.objects.create(
            filename="test_file2.jpg", target_org=org, upload_org=org, job_tree=folder_2, uploaded_by=standard_user
        )
        file_3 = FileList.objects.create(
            filename="test_file3.jpg", target_org=org, upload_org=org, job_tree=folder_2, uploaded_by=standard_user
        )

        standard = org.standard_key
        sh_us_node = StandardHeader.objects.get(header__name="UpstreamNode", standard=standard, header__type="asset")
        sh_ds_node = StandardHeader.objects.get(header__name="DownstreamNode", standard=standard, header__type="asset")
        sh_town = StandardHeader.objects.get(header__name="LocationTown", standard=standard, header__type="asset")
        sh_treet = StandardHeader.objects.get(header__name="LocationStreet", standard=standard, header__type="asset")
        sh_material = StandardHeader.objects.get(header__name="Material", standard=standard, header__type="asset")
        sh_diameter = StandardHeader.objects.get(header__name="HeightDiameter", standard=standard, header__type="asset")
        sh_asset_id = StandardHeader.objects.get(header__name="AssetID", standard=standard, header__type="asset")

        sh_date_captured = StandardHeader.objects.get(header__name="Date", standard=standard, header__type="inspection")
        sh_chainage = StandardHeader.objects.get(
            header__name="LengthSurveyed", standard=standard, header__type="inspection"
        )
        sh_direction = StandardHeader.objects.get(
            header__name="Direction", standard=standard, header__type="inspection"
        )
        sh_workorder = StandardHeader.objects.get(
            header__name="WorkOrder", standard=standard, header__type="inspection"
        )

        a1 = Asset.objects.create(organisation=org)
        a1.assetvalue_set.create(standard_header=sh_us_node, value="A1 Upstream")
        a1.assetvalue_set.create(standard_header=sh_ds_node, value="A1 Downstream")
        a1.assetvalue_set.create(standard_header=sh_town, value="Town1")
        a1.assetvalue_set.create(standard_header=sh_treet, value="Street1")
        a1.assetvalue_set.create(standard_header=sh_material, value="Vitrified Clay")
        a1.assetvalue_set.create(standard_header=sh_diameter, value="60.5")
        a1.assetvalue_set.create(standard_header=sh_asset_id, value="AssetID 1")

        a2 = Asset.objects.create(organisation=org)
        a2.assetvalue_set.create(standard_header=sh_us_node, value="A2 Upstream")
        a2.assetvalue_set.create(standard_header=sh_ds_node, value="A2 Downstream")
        a2.assetvalue_set.create(standard_header=sh_town, value="Shared Town")
        a2.assetvalue_set.create(standard_header=sh_treet, value="Street2")
        a2.assetvalue_set.create(standard_header=sh_material, value="Concrete")
        a2.assetvalue_set.create(standard_header=sh_diameter, value="100")
        a2.assetvalue_set.create(standard_header=sh_asset_id, value="AssetID 2")

        i1 = Inspection.objects.create(
            asset=a1, legacy_id=1500, folder=folder_1, file=file_1, structural_grade=4, service_grade=3
        )
        i1.inspectionvalue_set.create(standard_header=sh_date_captured, value="2025-01-01")
        i1.inspectionvalue_set.create(standard_header=sh_chainage, value="163.5")
        i1.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        i2 = Inspection.objects.create(
            asset=a1, legacy_id=None, folder=folder_2, file=file_2, status=StatusEnum.REVIEWED, structural_grade=3
        )
        i2.inspectionvalue_set.create(standard_header=sh_date_captured, value="bad date")
        i2.inspectionvalue_set.create(standard_header=sh_chainage, value="183.5")
        i2.inspectionvalue_set.create(standard_header=sh_direction, value="U")

        i3 = Inspection.objects.create(asset=a2, legacy_id=1501, folder=folder_2, file=file_3)  # Different asset
        i3.inspectionvalue_set.create(standard_header=sh_date_captured, value="2025-01-02")
        i3.inspectionvalue_set.create(standard_header=sh_chainage, value="100")
        i3.inspectionvalue_set.create(standard_header=sh_direction, value="D")
        i3.inspectionvalue_set.create(standard_header=sh_workorder, value="WO-123")

        # Force order of last_related_update fields
        i1.save()
        i2.save()
        i3.save()

        return [i1, i2, i3]

    @pytest.fixture
    def filter_obj(self, inspections, asset_owner_org, standard_user):
        all_folder_ids = [{"id": inspection.file.job_tree_id} for inspection in inspections]
        return InspectionFilter.objects.create(
            organisation=asset_owner_org,
            user=standard_user,
            filter_model={"common_filters": {}, "header_filters": []},
            folder_filter_model=all_folder_ids,
        )

    def test_no_filters(self, inspections, asset_owner_org, standard_user, client, inspection_settings):
        org = asset_owner_org
        client.force_login(user=standard_user)

        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == len(inspections)

        expected_insp_ids = {str(inspection.uuid) for inspection in inspections}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids, "All inspections should be returned"

    def test_search_matching_both_asset_ids(
        self, inspections, asset_owner_org, standard_user, client, inspection_settings
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id) + "&search=AssetID"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == len(inspections)

        expected_insp_ids = {str(inspection.uuid) for inspection in inspections}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert (
            actual_insp_ids == expected_insp_ids
        ), "All inspections should be returned - both asset ids share same prefix"

    def test_search_matching_one_asset_id(
        self, inspections, asset_owner_org, standard_user, client, inspection_settings
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id) + "&search=AssetID+2"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 1

        with_asset_id_2 = str(inspections[2].uuid)
        assert data["results"][0]["inspectionId"] == with_asset_id_2

    def test_search_matching_nothing(self, inspections, asset_owner_org, standard_user, client, inspection_settings):
        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id)
            + "&search=some+nonexistent+string"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 0

    def test_search_matching_multiple_fields(
        self, inspections, asset_owner_org, standard_user, client, inspection_settings
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id) + "&search=a")
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == len(inspections)

        expected_insp_ids = {str(inspection.uuid) for inspection in inspections}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids, "All inspections should be returned"

    def test_search_matching_work_order(self, inspections, asset_owner_org, standard_user, client, inspection_settings):
        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id) + "&search=WO-1")
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 1

        with_work_order = str(inspections[2].uuid)
        assert data["results"][0]["inspectionId"] == with_work_order

    def test_filter_folders_none(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        filter_obj.folder_filter_model = []
        filter_obj.save()

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 0, "No inspections should be returned since no folders are selected"

    def test_filter_folders_all(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        filter_obj.folder_filter_model = [{"id": inspections[0].folder_id}, {"id": inspections[2].folder_id}]
        filter_obj.save()

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 3

        expected_insp_ids = {str(inspection.uuid) for inspection in inspections}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids, "All inspections should be returned"

    def test_filter_folders_one(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        filter_obj.folder_filter_model = [{"id": inspections[0].folder_id}]
        filter_obj.save()

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 1

        expected_insp_ids = {str(inspections[0].uuid)}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids, "Only the inspection with the selected folder should be returned"

    def test_filter_status_one(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        filter_obj.filter_model["common_filters"] = {"status": {"value": ["Reviewed"], "operator": "IN"}}
        filter_obj.save()

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 1

        expected_insp_ids = {str(inspections[1].uuid)}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids, "Only the inspection with the 'Reviewed' status should be returned"

    def test_filter_status_none(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        filter_obj.filter_model["common_filters"] = {"status": {"value": [], "operator": "IN"}}
        filter_obj.save()

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == len(inspections)

        expected_insp_ids = {str(inspection.uuid) for inspection in inspections}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids, "All Inspections should be returned"

    def test_filter_status_multiple(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        filter_obj.filter_model["common_filters"] = {"status": {"value": ["Reviewed", "Uploaded"], "operator": "IN"}}
        filter_obj.save()

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == len(inspections)

        expected_insp_ids = {str(insp.uuid) for insp in inspections}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids, "All inspections have 'Reviewed' or 'Uploaded' status in this case"

    def test_filter_uuid_single(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        insp_id = str(inspections[0].uuid)

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id) + f"&uuid__in={insp_id}"
        )

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 1
        assert data["results"][0]["inspectionId"] == insp_id

    def test_filter_uuid_multiple(
        self, inspections, asset_owner_org, standard_user, client, filter_obj, inspection_settings
    ):
        param = f"{inspections[0].uuid},{inspections[1].uuid}"

        org = asset_owner_org
        client.force_login(user=standard_user)
        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id) + f"&uuid__in={param}"
        )

        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 2

        expected_insp_ids = {str(inspections[0].uuid), str(inspections[1].uuid)}
        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        assert actual_insp_ids == expected_insp_ids

    @pytest.mark.parametrize(
        ("case_name", "header_filters", "expected_inspection_idxs"),
        [
            (
                "AssetID startswith 'A'",
                [{"operator": "SW", "type": "asset", "name": "AssetID", "value": ["A"]}],
                {0, 1, 2},
            ),
            ("AssetID contains '1'", [{"operator": "CT", "type": "asset", "name": "AssetID", "value": ["1"]}], {0, 1}),
            (
                "AssetID is 'AssetID 1'",
                [{"operator": "EQ", "type": "asset", "name": "AssetID", "value": ["AssetID 1"]}],
                {0, 1},
            ),
            (
                "AssetID one-of single",
                [{"operator": "IN", "type": "asset", "name": "AssetID", "value": ["AssetID 2"]}],
                {2},
            ),
            (
                "AssetID one-of multiple",
                [{"operator": "IN", "type": "asset", "name": "AssetID", "value": ["AssetID 1", "AssetID 2"]}],
                {0, 1, 2},
            ),
            ("AssetID contains 'Z'", [{"operator": "CT", "type": "asset", "name": "AssetID", "value": ["Z"]}], set()),
            (
                "Inspection Date is 2025-01-01",
                [{"operator": "EQ", "type": "inspection", "name": "Date", "value": ["2025-01-01"]}],
                {0},
            ),
            (
                "Inspection Date is 2025-01-02",
                [{"operator": "IN", "type": "inspection", "name": "Date", "value": ["2025-01-02"]}],
                {2},
            ),
            (
                "Inspection LengthSurveyed is 100",
                [
                    {
                        "operator": "EQ",
                        "type": "inspection",
                        "data_type": "number",
                        "name": "LengthSurveyed",
                        "value": ["100"],
                    }
                ],
                {2},
            ),
            (
                "Inspection LengthSurveyed is 'not a number'",
                [
                    {
                        "operator": "EQ",
                        "type": "inspection",
                        "data_type": "number",
                        "name": "LengthSurveyed",
                        "value": ["not a number..."],
                    }
                ],
                set(),
            ),
            (
                "Inspection LengthSurveyed one-of multiple",
                [
                    {
                        "operator": "IN",
                        "type": "inspection",
                        "data_type": "number",
                        "name": "LengthSurveyed",
                        "value": ["not a number...", "5", "163.5", "100"],
                    }
                ],
                {0, 2},
            ),
            (
                "Complex combination",
                [
                    {
                        "operator": "IN",
                        "type": "inspection",
                        "data_type": "number",
                        "name": "StructuralGrade",
                        "value": ["3", "4"],
                    },
                    {
                        "operator": "EQ",
                        "type": "inspection",
                        "data_type": "number",
                        "name": "ServiceGrade",
                        "value": ["3"],
                    },
                    {
                        "operator": "IN",
                        "type": "inspection",
                        "name": "Direction",
                        "data_type": "options",
                        "value": ["U", "X"],
                    },
                ],
                {0},
            ),
            (
                "Filter direction generic",
                [
                    {
                        "operator": "EQ",
                        "type": "inspection",
                        "name": "Direction",
                        "data_type": "string",
                        "value": ["Upstream"],
                    }
                ],
                {0, 1},
            ),
            (
                "Filter direction multiple generic",
                [
                    {
                        "operator": "IN",
                        "type": "inspection",
                        "name": "Direction",
                        "data_type": "string",
                        "value": ["Upstream", "Downstream"],
                    }
                ],
                {0, 1, 2},
            ),
            (
                "Filter direction generic not matching",
                [
                    {
                        "operator": "IN",
                        "type": "inspection",
                        "name": "Direction",
                        "data_type": "string",
                        "value": ["Unknown", "something else"],
                    }
                ],
                set(),
            ),
        ],
    )
    def test_header_filters(
        self,
        case_name: str,
        header_filters: list[dict],
        expected_inspection_idxs: set[int],
        inspections,
        asset_owner_org,
        standard_user,
        client,
        filter_obj,
        inspection_settings,
    ):
        filter_obj.filter_model["header_filters"] = header_filters
        filter_obj.save()

        org = asset_owner_org
        client.force_login(user=standard_user)

        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()

        actual_insp_ids = {result["inspectionId"] for result in data["results"]}
        actual_idxs = {i for i, insp in enumerate(inspections) if str(insp.uuid) in actual_insp_ids}
        assert (
            actual_idxs == expected_inspection_idxs
        ), f"{case_name}: Only the inspections that match the filter should be returned"

    @pytest.mark.parametrize(
        ("field", "order"),
        [
            ("chainage", [2, 0, 1]),
            ("-chainage", [1, 0, 2]),
            ("direction", [2, 1, 0]),
            # Ordering is not the opposite of ASC as inspections with the same values are orderby ID.
            # New-style ID ordering from the DB will always put them last/first as they are treated as
            # 'null' to get current ID ordering working by converting them to numberic values.
            ("-direction", [1, 0, 2]),
            ("date_captured", [0, 2, 1]),
            ("-date_captured", [1, 2, 0]),
        ],
    )
    def test_inspection_value_ordering(
        self, client, standard_user, asset_owner_org, inspection_settings, inspections, field, order
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)

        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id)
            + f"&ordering={field}&page_size=2&page=1"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert len(data["results"]) == 2

        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id)
            + f"&ordering={field}&page_size=2&page=2"
        )
        assert res.status_code == status.HTTP_200_OK
        data["results"].extend(res.json()["results"])
        assert len(data["results"]) == 3

        assert data["results"][0]["inspectionId"] == str(inspections[order[0]].uuid)
        assert data["results"][1]["inspectionId"] == str(inspections[order[1]].uuid)
        assert data["results"][2]["inspectionId"] == str(inspections[order[2]].uuid)

    @pytest.mark.parametrize(
        ("field", "order"),
        [
            ("material", [2, 1, 0]),
            ("-material", [1, 0, 2]),
            ("asset_id", [1, 0, 2]),
            ("-asset_id", [2, 1, 0]),
            ("location_street", [1, 0, 2]),
            ("-location_street", [2, 1, 0]),
            ("location_town", [2, 1, 0]),
            ("-location_town", [1, 0, 2]),
            ("downstream_node", [1, 0, 2]),
            ("-downstream_node", [2, 1, 0]),
            ("upstream_node", [1, 0, 2]),
            ("-upstream_node", [2, 1, 0]),
            ("diameter", [1, 0, 2]),
            ("-diameter", [2, 1, 0]),
        ],
    )
    def test_asset_value_ordering(
        self, client, standard_user, asset_owner_org, inspection_settings, inspections, field, order
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)

        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id)
            + f"&ordering={field}&page_size=2&page=1"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert len(data["results"]) == 2

        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id)
            + f"&ordering={field}&page_size=2&page=2"
        )
        assert res.status_code == status.HTTP_200_OK
        data["results"].extend(res.json()["results"])
        assert len(data["results"]) == 3

        assert data["results"][0]["inspectionId"] == str(inspections[order[0]].uuid)
        assert data["results"][1]["inspectionId"] == str(inspections[order[1]].uuid)
        assert data["results"][2]["inspectionId"] == str(inspections[order[2]].uuid)

    @pytest.mark.parametrize(
        ("field", "order", "use_header_names"),
        [
            ("filename", [0, 1, 2], True),
            ("filename", [0, 1, 2], False),
            ("-filename", [2, 1, 0], True),
            ("-filename", [2, 1, 0], False),
            ("created_time", [0, 1, 2], True),
            ("created_time", [0, 1, 2], False),
            ("-created_time", [2, 1, 0], True),
            ("-created_time", [2, 1, 0], False),
            ("id", [0, 2, 1], True),
            ("id", [0, 2, 1], False),
            ("-id", [1, 2, 0], True),
            ("-id", [1, 2, 0], False),
            ("condition_rating", [2, 1, 0], True),
            ("condition_rating", [2, 1, 0], False),
            ("-condition_rating", [0, 1, 2], True),
            ("-condition_rating", [0, 1, 2], False),
            ("service_condition_rating", [1, 2, 0], True),
            ("service_condition_rating", [1, 2, 0], False),
            ("-service_condition_rating", [0, 1, 2], True),
            ("-service_condition_rating", [0, 1, 2], False),
            ("created_at", [0, 1, 2], True),
            ("created_at", [0, 1, 2], False),
            ("-created_at", [2, 1, 0], True),
            ("-created_at", [2, 1, 0], False),
            ("status", [1, 2, 0], True),
            ("status", [1, 2, 0], False),
            ("-status", [2, 0, 1], True),
            ("-status", [2, 0, 1], False),
            ("diameter", [1, 0, 2], True),
            ("diameter", [1, 0, 2], False),
            ("-diameter", [2, 1, 0], True),
            ("-diameter", [2, 1, 0], False),
            ("chainage", [2, 0, 1], True),
            ("chainage", [2, 0, 1], False),
            ("-chainage", [1, 0, 2], True),
            ("-chainage", [1, 0, 2], False),
            ("date_captured", [0, 2, 1], True),
            ("date_captured", [0, 2, 1], False),
            ("-date_captured", [1, 2, 0], True),
            ("-date_captured", [1, 2, 0], False),
            ("material", [2, 1, 0], True),
            ("material", [2, 1, 0], False),
            ("-material", [1, 0, 2], True),
            ("-material", [1, 0, 2], False),
            ("last_related_update", [0, 1, 2], True),
            ("last_related_update", [0, 1, 2], False),
            ("-last_related_update", [2, 1, 0], True),
            ("-last_related_update", [2, 1, 0], False),
        ],
    )
    def test_inspection_ordering(
        self,
        client: Client,
        standard_user: CustomUser,
        asset_owner_org: Organisations,
        inspection_settings: InspectionSettings,
        inspections: list[InspectionModel],
        field: str,
        order: list[int],
        use_header_names: bool,
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)

        id_field = "uuid" if use_header_names else "inspectionId"

        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id)
            + f"&ordering={field}&page_size=2&page=1&use_header_names={use_header_names}"
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert len(data["results"]) == 2

        res = client.get(
            path=inspection_settings.inspection_list_url.format(organisation_id=org.id)
            + f"&ordering={field}&page_size=2&page=2&use_header_names={use_header_names}"
        )
        assert res.status_code == status.HTTP_200_OK
        data["results"].extend(res.json()["results"])
        assert len(data["results"]) == 3

        assert data["results"][0][id_field] == str(inspections[order[0]].uuid)
        assert data["results"][1][id_field] == str(inspections[order[1]].uuid)
        assert data["results"][2][id_field] == str(inspections[order[2]].uuid)

    def test_status_counts_with_no_header_filters(
        self, client, standard_user, asset_owner_org, inspection_settings, inspections
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)

        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()

        assert data["statusCounts"] == {
            "Reviewed": 1,
            "Uploaded": 2,
        }

    def test_status_counts_updated_by_header_filters(
        self, client, standard_user, asset_owner_org, filter_obj, inspections, inspection_settings
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)

        filter_obj.filter_model["common_filters"] = {"status": {"value": ["Reviewed"], "operator": "IN"}}
        filter_obj.save()

        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()

        assert len(data["results"]) == 1
        assert data["statusCounts"] == {
            "Reviewed": 1,
        }, "The status counts should reflect the applied filters"

    def test_status_counts_unaffected_by_pagination(
        self, client, standard_user, asset_owner_org, inspections, inspection_settings
    ):
        org = asset_owner_org
        client.force_login(user=standard_user)

        res = client.get(path=inspection_settings.inspection_list_url.format(organisation_id=org.id) + "&page_size=2")
        assert res.status_code == status.HTTP_200_OK
        data = res.json()

        assert len(data["results"]) == 2
        assert data["statusCounts"] == {
            "Reviewed": 1,
            "Uploaded": 2,
        }, "The status counts should not be affected by pagination"


def test_delete_inspection(client, inspection_settings, asset_owner_org, service_user_key):
    org = asset_owner_org

    asset = factory.create_assets(org=org)[0]
    inspections = factory.create_bulk_inspections(asset, n=10)

    target_insp = Inspection.objects.get(uuid=inspections[0]["inspection_id"])
    other_ids = [insp["inspection_id"] for insp in inspections[1:]]

    insp_count_before = Inspection.objects.count()
    other_insp_value_count_before = InspectionValue.objects.filter(inspection__uuid__in=other_ids).count()
    other_mpl_count_before = MapPointList.objects.filter(inspection__uuid__in=other_ids).count()

    res = client.delete(
        inspection_settings.inspection_url.format(inspection_uuid=str(target_insp.uuid)),
        HTTP_X_API_KEY=service_user_key,
    )
    assert res.status_code == status.HTTP_204_NO_CONTENT

    with pytest.raises(Inspection.DoesNotExist):  # noqa - linter is confused because this is added dynamically
        target_insp.refresh_from_db()  # Inspection should be deleted

    assert not MapPointList.objects.filter(inspection__uuid=target_insp.uuid).exists(), "MPL should be deleted"

    insp_value_count_after = InspectionValue.objects.filter(inspection__uuid__in=other_ids).count()

    assert Inspection.objects.count() == insp_count_before - 1, "One inspection should be deleted"
    assert insp_value_count_after == other_insp_value_count_before, "Other inspections' values should not be deleted"
    assert (
        MapPointList.objects.filter(inspection__uuid__in=other_ids).count() == other_mpl_count_before
    ), "Other inspections' MPLs should not be deleted"


def test_non_service_user_cannot_delete_inspection(client, inspection_settings, asset_owner_org, standard_user):
    org = asset_owner_org

    asset = factory.create_assets(org=org)[0]
    inspections = factory.create_bulk_inspections(asset, n=1)

    target_insp = Inspection.objects.get(uuid=inspections[0]["inspection_id"])

    client.force_login(user=standard_user)
    res = client.delete(inspection_settings.inspection_url.format(inspection_uuid=str(target_insp.uuid)))
    assert res.status_code == status.HTTP_403_FORBIDDEN

    target_insp.refresh_from_db()  # Inspection should still exist


def test_file_patch_multiple_fields(
    service_user_key, inspection_settings, asset_owner_org, root_folder_for_standard_user
):
    org = asset_owner_org
    client = Client()

    file = FileList.objects.create(
        filename="test_file",
        target_org=org,
        job_tree=root_folder_for_standard_user,
        file_size="0",
        file_type="video/mp4",
    )

    resp = client.patch(
        path=inspection_settings.get_file_detail_url(file.id),
        data={"fileSize": "123.4MB", "processingCompletedTime": "2025-01-01T00:00:00Z"},
        content_type="application/json",
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=org.id,
    )
    assert resp.status_code == status.HTTP_204_NO_CONTENT

    file.refresh_from_db()
    assert file.file_size == "123.4MB"
    assert file.processing_completed_time == datetime(2025, 1, 1, tzinfo=timezone.utc)
    assert file.file_type == "video/mp4", "File type should not change"


def test_processing_file_create_nonexistent_file(standard_user, inspection_settings):
    client = Client()
    client.force_login(user=standard_user)

    resp = client.post(
        path=inspection_settings.processing_files_url,
        data={"fileId": 12345, "status": "Uploading"},
        content_type="application/json",
    )
    assert resp.status_code == status.HTTP_400_BAD_REQUEST


def test_processing_file_create_already_exists(standard_user, inspection_settings):
    client = Client()
    client.force_login(user=standard_user)

    file = FileList.objects.create(
        filename="test_file",
        target_org=standard_user.organisation,
        job_tree=JobsTree.add_root(primary_org=standard_user.organisation, created_date=dj_timezone.now()),
    )
    ProcessingList.objects.create(associated_file=file, status="Storing Results")

    resp = client.post(
        path=inspection_settings.processing_files_url,
        data={"fileId": file.id, "status": "Uploading"},
        content_type="application/json",
    )
    assert resp.status_code == status.HTTP_400_BAD_REQUEST


def test_processing_file_create_successful(standard_user, inspection_settings):
    client = Client()
    client.force_login(user=standard_user)

    file = FileList.objects.create(
        filename="test_file",
        target_org=standard_user.organisation,
        job_tree=JobsTree.add_root(primary_org=standard_user.organisation, created_date=dj_timezone.now()),
    )

    resp = client.post(
        path=inspection_settings.processing_files_url,
        data={"fileId": file.id, "status": "Uploading", "manualQaRequired": True},
        content_type="application/json",
    )
    assert resp.status_code == status.HTTP_201_CREATED

    processing_record = ProcessingList.objects.get(associated_file=file)
    assert processing_record.status == "Uploading"
    assert processing_record.associated_file == file
    assert processing_record.target_org == file.target_org
    assert processing_record.file_size == file.file_size
    assert processing_record.manual_qa_required is True
    assert processing_record.sewer_data is False

    res_data = resp.json()
    assert res_data["fileListId"] == file.id
    assert res_data["id"] == processing_record.id
    assert res_data["status"] == "Uploading"
    assert res_data["targetOrg"] == file.target_org.id
    assert res_data["fileSize"] == file.file_size
    assert res_data["manualQaRequired"] is True
    assert res_data["sewerData"] is False


def test_patch_processing_file(inspection_settings, standard_user, asset_owner_org, root_folder_for_standard_user):
    org = asset_owner_org
    file = FileList.objects.create(
        filename="test_file", target_org=standard_user.organisation, job_tree=root_folder_for_standard_user
    )
    processing_record = ProcessingList.objects.create(
        associated_file=file,
        status="Uploading",
        target_org=org,
    )
    f2 = FileList.objects.create(
        filename="test_file2", target_org=standard_user.organisation, job_tree=root_folder_for_standard_user
    )
    ProcessingList.objects.create(
        associated_file=f2,
        status="Uploading",
        target_org=org,
    )

    client = Client()
    client.force_login(user=standard_user)

    res = client.patch(
        inspection_settings.get_file_processing_url(processing_record.id),
        data={
            "status": "Analysing Video",
        },
        content_type="application/json",
    )
    assert res.status_code == status.HTTP_204_NO_CONTENT

    processing_record.refresh_from_db()
    assert processing_record.status == "Analysing Video"
    assert processing_record.associated_file.filename == "test_file"
    assert processing_record.target_org == org

    assert f2.processing_record.status == "Uploading"


@pytest.mark.parametrize("create_initial_value", [True, False])
def test_patch_inspection_detail_setup_location(
    client, inspection_settings: "InspectionSettings", standard_user, create_initial_value: bool
):
    """
    Test PATCH requests to update inspection direction and verify setup location behavior.
    Tests both creation and update scenarios with different header formats.
    """

    def setup_inspection_data() -> tuple[Inspection, StandardHeader]:
        standard: Standard = Standard.objects.get(name="NZ Pipe Manual 4")

        # Get standard headers
        headers: dict[str, StandardHeader] = {
            "upstream_node": StandardHeader.objects.get(
                header__name="UpstreamNode", standard=standard, header__type="asset"
            ),
            "downstream_node": StandardHeader.objects.get(
                header__name="DownstreamNode", standard=standard, header__type="asset"
            ),
            "standard": StandardHeader.objects.get(
                header__name="Standard", standard=standard, header__type="inspection"
            ),
            "setup_location": StandardHeader.objects.get(
                header__name="SetupLocation", standard=standard, header__type="inspection"
            ),
        }

        # Create asset with values
        asset: Asset = Asset.objects.create(organisation=standard_user.organisation)
        asset.assetvalue_set.bulk_create(
            [
                AssetValue(standard_header=headers["upstream_node"], value="1", original_value="1", asset=asset),
                AssetValue(standard_header=headers["downstream_node"], value="2", original_value="2", asset=asset),
            ]
        )

        # Create inspection with values
        inspection: Inspection = Inspection.objects.create(asset=asset)
        inspection_values: list[InspectionValue] = [
            InspectionValue(
                standard_header=headers["standard"], value="NZ V4", original_value="NZ V4", inspection=inspection
            )
        ]
        if create_initial_value:
            inspection_values.append(
                InspectionValue(
                    standard_header=headers["setup_location"], value="U", original_value="U", inspection=inspection
                )
            )
        inspection.inspectionvalue_set.bulk_create(inspection_values)

        # Create map point and set legacy ID
        map_point: MapPointList = MapPointList.objects.create(inspection=inspection, standard_key=standard)
        inspection.legacy_id = map_point.id
        inspection.save()

        return inspection, headers["setup_location"]

    client.force_login(user=standard_user)
    inspection: Inspection
    setup_location_header: StandardHeader
    inspection, setup_location_header = setup_inspection_data()
    url: str = inspection_settings.inspection_url.format(inspection_uuid=inspection.uuid)

    # Define Test cases
    test_cases = [
        {
            "description": "Initial patch with direction=Upstream",
            "payload": {"direction": "Upstream"},
            "expected_direction": "Upstream",
            "expected_setup": "Downstream",
            "database_value": "D",
            "use_headers": False,
        },
        {
            "description": "Second patch with direction=Downstream",
            "payload": {"direction": "Downstream"},
            "expected_direction": "Downstream",
            "expected_setup": "Upstream",
            "database_value": "U",
            "use_headers": False,
        },
        {
            "description": "Third patch with direction=Unknown with header names",
            "payload": {"Direction": "Unknown"},
            "expected_direction": "Unknown",
            "expected_setup": "Unknown",
            "database_value": "Q",
            "use_headers": True,
        },
        {
            "description": "Fourth patch with direction=Upstream with header names",
            "payload": {"Direction": "Upstream"},
            "expected_direction": "Upstream",
            "expected_setup": "Downstream",
            "database_value": "D",
            "use_headers": True,
        },
    ]

    for test_case in test_cases:
        # Construct URL with optional header parameter
        test_url: str = f"{url}?use_header_names=true" if test_case["use_headers"] else url

        # PATCH request
        response = client.patch(
            path=test_url,
            data=test_case["payload"],
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK, f"{test_case['description']} failed status check"

        # Verify database value
        setup_value: InspectionValue = InspectionValue.objects.filter(
            inspection_id=inspection.uuid, standard_header=setup_location_header
        ).first()
        assert (
            setup_value.value == test_case["database_value"]
        ), f"{test_case['description']} failed setup location value check"

        # Verify PATCH response
        data = response.json()
        direction_key: str = "Direction" if test_case["use_headers"] else "direction"
        setup_key: str = "SetupLocation" if test_case["use_headers"] else "setupLocation"

        assert (
            data[direction_key] == test_case["expected_direction"]
        ), f"{test_case['description']} failed direction assertion"
        assert (
            data[setup_key] == test_case["expected_setup"]
        ), f"{test_case['description']} failed setup location assertion"

        # Verify GET response
        response = client.get(test_url)
        assert response.status_code == status.HTTP_200_OK, f"{test_case['description']} failed GET status"

        data = response.json()
        direction_key: str = "Direction" if test_case["use_headers"] else "direction"
        setup_key: str = "SetupLocation" if test_case["use_headers"] else "setupLocation"

        assert (
            data[direction_key] == test_case["expected_direction"]
        ), f"{test_case['description']} failed direction assertion"
        assert (
            data[setup_key] == test_case["expected_setup"]
        ), f"{test_case['description']} failed setup location assertion"


def test_inspection_model_with_setup_location(standard_user):
    standard = Standard.objects.get(name="NZ Pipe Manual 4")

    sh_upstream_node = StandardHeader.objects.get(header__name="UpstreamNode", standard=standard, header__type="asset")
    sh_downstream_node = StandardHeader.objects.get(
        header__name="DownstreamNode", standard=standard, header__type="asset"
    )
    sh_standard = StandardHeader.objects.get(header__name="Standard", standard=standard, header__type="inspection")
    sh_setup_location = StandardHeader.objects.get(
        header__name="SetupLocation", standard=standard, header__type="inspection"
    )

    a1 = Asset.objects.create(organisation=standard_user.organisation)
    a1.assetvalue_set.create(
        standard_header=sh_upstream_node,
        value="1",
        original_value="1",
    )
    a1.assetvalue_set.create(
        standard_header=sh_downstream_node,
        value="2",
        original_value="2",
    )
    a1.save()
    i1 = Inspection.objects.create(asset=a1)
    i1.inspectionvalue_set.create(
        standard_header=sh_setup_location,
        value="U",
        original_value="U",
    )
    i1.inspectionvalue_set.create(
        standard_header=sh_standard,
        value="NZ V4",
        original_value="NZ V4",
    )
    m1 = MapPointList.objects.create(inspection=i1, standard_key=standard)
    i1.legacy_id = m1.id
    i1.save()

    inspection_values = i1.inspectionvalue_set.all()
    inspection_details = {
        inspection_value.standard_header.header.name: inspection_value.value for inspection_value in inspection_values
    }
    assert "SetupLocation" in inspection_details
    assert "Direction" not in inspection_details
    inspection_details = get_inspection_details(i1)
    assert "Direction" in inspection_details
    assert inspection_details["Direction"] == DirectionEnum.DOWNSTREAM.value


class TestInspectionLastRelatedUpdate:
    """
    Tests to ensure that the 'last_related_update' field on the Inspection model is correctly kept up to date
    """

    @pytest.fixture
    def inspections(self, asset_owner_org) -> list[Inspection]:
        """One inspection on its own asset, two others sharing an asset"""
        org = asset_owner_org
        assets = factory.create_assets(org=org, n=2)
        first_insp = factory.create_bulk_inspections(assets[0], n=1)[0]
        inspections = [Inspection.objects.get(uuid=first_insp["inspection_id"])]
        inspections.extend(
            Inspection.objects.get(uuid=insp["inspection_id"])
            for insp in factory.create_bulk_inspections(assets[1], n=2)
        )

        return inspections

    def test_filter_inspection_list_by_last_updated(self, client, standard_user, inspection_settings):
        assets = factory.create_assets(org=standard_user.organisation, n=1)
        factory.create_bulk_inspections(assets[0], n=2)

        now = datetime.now(tz=timezone.utc)

        client.force_login(user=standard_user)
        request_path = inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation.id)
        resp = client.get(
            path=f"{request_path}&last_related_update__gte={now.strftime('%Y-%m-%d')}",
            content_type="application/json",
        )
        assert resp.status_code == status.HTTP_200_OK
        res_data = resp.json()["results"]
        assert len(res_data) == 2

        client.force_login(user=standard_user)
        request_path = inspection_settings.inspection_list_url.format(organisation_id=standard_user.organisation.id)
        resp = client.get(
            path=f"{request_path}&last_related_update__gte={(now + timedelta(days=1)).strftime('%Y-%m-%d')}",
            content_type="application/json",
        )
        assert resp.status_code == status.HTTP_200_OK
        res_data = resp.json()["results"]
        assert len(res_data) == 0

    def test_updated_on_inspection_value_update(self, inspections: list[Inspection]):
        inspection = inspections[0]
        before = inspection.last_related_update
        before_on_other = inspections[1].last_related_update

        val = inspection.inspectionvalue_set.get(standard_header__header__name="Direction")
        val.value = "U"
        val.save()

        inspection.refresh_from_db()

        assert inspection.last_related_update > before, "Inspection should be updated"
        assert inspections[1].last_related_update == before_on_other, "Other inspections should not be updated"

    def test_updated_on_asset_update(self, inspections: list[Inspection]):
        first_insp_before = inspections[0].last_related_update
        second_insp_before = inspections[1].last_related_update
        third_insp_before = inspections[2].last_related_update

        inspections[1].asset.type = "manhole"
        inspections[1].asset.save()

        inspections[0].refresh_from_db()
        inspections[1].refresh_from_db()
        inspections[2].refresh_from_db()

        assert inspections[0].last_related_update == first_insp_before, "This inspection should not be updated"
        assert inspections[1].last_related_update > second_insp_before, "This inspection had its asset updated"
        assert inspections[2].last_related_update > third_insp_before, "This inspection had its asset updated"

    def test_updated_on_asset_value_update(self, inspections: list[Inspection]):
        first_insp_before = inspections[0].last_related_update
        second_insp_before = inspections[1].last_related_update
        third_insp_before = inspections[2].last_related_update

        asset = inspections[1].asset
        val = asset.assetvalue_set.get(standard_header__header__name="UpstreamNode")
        val.value = "new value"
        val.save()

        inspections[0].refresh_from_db()
        inspections[1].refresh_from_db()
        inspections[2].refresh_from_db()

        assert inspections[0].last_related_update == first_insp_before, "This inspection should not be updated"
        assert inspections[1].last_related_update > second_insp_before, "This inspection had its asset's value updated"
        assert inspections[2].last_related_update > third_insp_before, "This inspection had its asset's value updated"

    def test_updated_on_file_update(self, inspections: list[Inspection]):
        first_insp_before = inspections[0].last_related_update
        second_insp_before = inspections[1].last_related_update
        third_insp_before = inspections[2].last_related_update

        inspections[0].file.play_url = "some new value"
        inspections[0].file.save()

        inspections[0].refresh_from_db()
        inspections[1].refresh_from_db()
        inspections[2].refresh_from_db()

        assert inspections[0].last_related_update > first_insp_before, "This inspection should be updated"
        assert inspections[1].last_related_update == second_insp_before, "This inspection should not be updated"
        assert inspections[2].last_related_update == third_insp_before, "This inspection should not be updated"

    def test_updated_on_video_frame_update(self, inspections: list[Inspection]):
        first_insp_before = inspections[0].last_related_update
        second_insp_before = inspections[1].last_related_update
        third_insp_before = inspections[2].last_related_update

        inspections[0].file.videoframes_set.create(class_certainty=0.9)

        inspections[0].refresh_from_db()
        inspections[1].refresh_from_db()
        inspections[2].refresh_from_db()

        assert inspections[0].last_related_update > first_insp_before, "This inspection should be updated"
        assert inspections[1].last_related_update == second_insp_before, "This inspection should not be updated"
        assert inspections[2].last_related_update == third_insp_before, "This inspection should not be updated"
