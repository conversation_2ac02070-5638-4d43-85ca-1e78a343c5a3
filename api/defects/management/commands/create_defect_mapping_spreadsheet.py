from pathlib import Path

from django.core.management.base import BaseCommand

from api.defects.models import DefectModelList, DefectScores, StandardSubcategory, Standard


MANAGEMENT_FOLDER = Path(__file__).parent
DATA_FOLDER = MANAGEMENT_FOLDER.parent / "baseline_data"


def get_dml_stf_desc(dml):
    return dml["fields"]["name"].split(" - ", 1)[0]



class Command(BaseCommand):
    help = "Create a spreadsheet for mapping defects to the new schema for a single substandard."

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        """Handle the command execution."""

        