from django.db import models


class Feature(models.Model):
    """
    A Feature is a top-level element that can be observed during an inspection.

    Features are standard-independent.
    """

    key = models.CharField(max_length=50, unique=True, editable=False)
    """A stable identifier for internal programmatic access"""

    display_name = models.Char<PERSON>ield(max_length=255)
    """
    User facing name of the feature
    """

    created_at = models.DateTimeField(auto_now_add=True)


class SubFeatureKind(models.TextChoices):
    CATEGORICAL = "CAT", "Categorical"
    NUMERIC = "NUM", "Numeric"


class SubFeatureNumericKind(models.TextChoices):
    """
    Base units:
    DISTANCE: metres
    LENGTH: millimetres
    PERCENTAGE: percent
    COUNT: number
    VOLUME: cubic metres
    ANGLE: degrees

    DISTANCE is for larger scale measurements, and LENGTH for smaller scale.
    """

    DISTANCE = "DIST", "Distance"
    LENGTH = "LEN", "Length"
    PERCENTAGE = "PERC", "Percentage"
    COUNT = "COUNT", "Count"
    VOLUME = "VOL", "Volume"
    ANGLE = "ANGLE", "Rotational Angle"

    def get_base_unit(self) -> "SubFeatureUnit":
        match self:
            case SubFeatureNumericKind.DISTANCE:
                return SubFeatureUnit.METRE
            case SubFeatureNumericKind.LENGTH:
                return SubFeatureUnit.MILLIMETRE
            case SubFeatureNumericKind.PERCENTAGE:
                return SubFeatureUnit.PERC
            case SubFeatureNumericKind.COUNT:
                return SubFeatureUnit.COUNT
            case SubFeatureNumericKind.VOLUME:
                return SubFeatureUnit.CUBIC_METRE
            case SubFeatureNumericKind.ANGLE:
                return SubFeatureUnit.DEGREES
            case _:
                raise ValueError(f"Unexpected SubFeatureNumericKind: {self}")


class SubFeatureUnit(models.TextChoices):
    """
    Units for numeric sub-features
    """

    PERC = "PERC", "Percentage"
    METRE = "METRE", "Metres"
    MILLIMETRE = "MM", "Millimetres"
    FEET = "FEET", "Feet"
    INCHES = "INCH", "Inches"
    COUNT = "COUNT", "Count"
    CUBIC_METRE = "CUBIC_M", "Cubic Metres"
    CUBIC_FEET = "CUBIC_FT", "Cubic Feet"
    DEGREES = "DEGREES", "Degrees"

    def get_display_suffix(self) -> str:
        match self:
            case SubFeatureUnit.PERC:
                return "%"
            case SubFeatureUnit.METRE:
                return "m"
            case SubFeatureUnit.FEET:
                return "ft"
            case SubFeatureUnit.INCHES:
                return "in"
            case SubFeatureUnit.COUNT:
                return ""
            case SubFeatureUnit.CUBIC_METRE:
                return "m³"
            case SubFeatureUnit.CUBIC_FEET:
                return "ft³"
            case SubFeatureUnit.DEGREES:
                return "°"
            case _:
                raise ValueError(f"Unexpected SubFeatureUnit: {self}")

    def get_numeric_kind(self) -> SubFeatureNumericKind:
        match self:
            case SubFeatureUnit.PERC:
                return SubFeatureNumericKind.PERCENTAGE
            case SubFeatureUnit.METRE | SubFeatureUnit.FEET:
                return SubFeatureNumericKind.DISTANCE
            case SubFeatureUnit.MILLIMETRE | SubFeatureUnit.INCHES:
                return SubFeatureNumericKind.LENGTH
            case SubFeatureUnit.COUNT:
                return SubFeatureNumericKind.COUNT
            case SubFeatureUnit.CUBIC_METRE | SubFeatureUnit.CUBIC_FEET:
                return SubFeatureNumericKind.VOLUME
            case SubFeatureUnit.DEGREES:
                return SubFeatureNumericKind.ANGLE
            case _:
                raise ValueError(f"Unexpected SubFeatureUnit: {self}")

    def convert_to_unit(self, value: float, to_unit: "SubFeatureUnit | None" = None) -> float:
        """
        Convert a value from this unit to another unit (or to the base unit if no target unit is specified).
        """
        if to_unit is None:
            kind = self.get_numeric_kind()
            to_unit = kind.get_base_unit()
        if self == to_unit:
            return value

        if (self, to_unit) in _UNIT_CONVERSION_FACTORS:
            factor = _UNIT_CONVERSION_FACTORS[(self, to_unit)]
        elif (to_unit, self) in _UNIT_CONVERSION_FACTORS:
            factor = 1.0 / _UNIT_CONVERSION_FACTORS[(to_unit, self)]
        else:
            raise ValueError(f"Cannot convert from {self} to {to_unit}")

        return value * factor


_UNIT_CONVERSION_FACTORS = {
    (SubFeatureUnit.METRE, SubFeatureUnit.MILLIMETRE): 1000.0,
    (SubFeatureUnit.METRE, SubFeatureUnit.FEET): 3.28084,
    (SubFeatureUnit.METRE, SubFeatureUnit.INCHES): 39.3701,
    (SubFeatureUnit.MILLIMETRE, SubFeatureUnit.FEET): 0.00328084,
    (SubFeatureUnit.MILLIMETRE, SubFeatureUnit.INCHES): 0.0393701,
    (SubFeatureUnit.FEET, SubFeatureUnit.INCHES): 12.0,
    (SubFeatureUnit.CUBIC_METRE, SubFeatureUnit.CUBIC_FEET): 35.3147,
}


class SubFeature(models.Model):
    """
    A SubFeature is a categorical or numeric variable that can be applied to a feature.

    SubFeatures are standard-independent.
    """

    Kind = SubFeatureKind
    NumericKind = SubFeatureNumericKind

    key = models.CharField(max_length=50, unique=True, editable=False)
    """
    A stable identifier for internal programmatic access
    """

    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    feature = models.ForeignKey(Feature, on_delete=models.PROTECT, related_name="sub_features")

    kind = models.CharField(max_length=3, choices=SubFeatureKind.choices)
    """
    Is this a numeric or categorical sub-feature?
    """

    # Fields only relevant to numeric sub-features:
    numeric_kind = models.CharField(max_length=10, null=True, blank=True, choices=SubFeatureNumericKind.choices)
    """
    What kind of numeric measurement this sub-feature represents
    """

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"<SubFeature: key='{self.key}' display_name='{self.display_name}' kind={self.kind} numeric_kind={self.numeric_kind}>"

    class Meta:
        constraints = [
            models.CheckConstraint(
                name="subfeature_kind_allowed_vals",
                check=models.Q(kind__in=[v[0] for v in SubFeatureKind.choices]),
            ),
            models.CheckConstraint(
                name="subfeature_numeric_kind_allowed_vals",
                check=(
                    models.Q(kind="CAT", numeric_kind__isnull=True)
                    | models.Q(kind="NUM", numeric_kind__in=[v[0] for v in SubFeatureNumericKind.choices])
                ),
            ),
        ]


class SubFeatureOption(models.Model):
    """
    A possible option for a categorical sub-feature.
    """

    key = models.CharField(max_length=50, unique=True, editable=False)
    """
    A stable identifier for internal programmatic access
    """

    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="options")

    display_name = models.CharField(max_length=255)
    display_order = models.PositiveIntegerField()

    created_at = models.DateTimeField(auto_now_add=True)


class MLLabelFeatureMapping(models.Model):
    """
    Which feature does a raw ML class label correspond to?
    """

    ml_label = models.CharField(blank=False, unique=True, max_length=255)
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE, related_name="mapped_ml_labels")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class MLLabelSubFeatureMapping(models.Model):
    """
    Which option (or numeric value / range) does a raw ML class label correspond to for a sub-feature?
    """

    ml_label = models.CharField(blank=False, max_length=255)

    sub_feature = models.ForeignKey(SubFeature, on_delete=models.CASCADE, related_name="mapped_sub_feature_ml_labels")
    option = models.ForeignKey(
        SubFeatureOption,
        null=True,
        default=None,
        on_delete=models.CASCADE,
        related_name="mapped_ml_labels",
    )

    numeric_min = models.FloatField(null=True, default=None)
    numeric_max = models.FloatField(null=True, default=None)
    numeric_value = models.FloatField(null=True, default=None)
    numeric_unit = models.CharField(max_length=10, null=True, default=None, choices=SubFeatureUnit.choices)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["ml_label", "sub_feature"],
                name="unique_ml_label_sub_feature_mapping",
            ),
            # Either option is set, or one of numeric_value or numeric_min/max is set
            models.CheckConstraint(
                name="ml_label_sub_feature_mapping_option_or_numeric",
                check=(
                    models.Q(option__isnull=False)
                    | (
                        models.Q(numeric_value__isnull=False)
                        | models.Q(numeric_min__isnull=False)
                        | models.Q(numeric_max__isnull=False)
                    )
                ),
            ),
            models.CheckConstraint(
                name="ml_label_sub_feature_mapping_numeric_unit",
                check=models.Q(numeric_unit__isnull=True)
                | models.Q(numeric_unit__in=[v[0] for v in SubFeatureUnit.choices]),
            ),
        ]
