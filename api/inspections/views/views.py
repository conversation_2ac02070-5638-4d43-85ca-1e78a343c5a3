# pylint: disable=C0302
import base64
import json
import logging
import re
import time
import uuid
from datetime import datetime
from functools import reduce
from io import StringIO
from typing import Any, Set

import jsonschema
import pandas as pd
import pydantic
import requests
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.contrib.auth.models import AnonymousUser
from django.core.cache import caches
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Count, Q, QuerySet, Subquery
from django.db.transaction import atomic
from django.http import Http404, HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.conf import settings
from django_filters.rest_framework import DjangoFilterBackend
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON>J<PERSON>NRenderer
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiRequest
from pydantic import BaseModel, field_validator
from rest_framework import serializers, status
from rest_framework.exceptions import (
    NotFound,
    ParseError,
    PermissionDenied,
    ValidationError,
)
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.generics import (
    CreateAPIView,
    ListAPIView,
    RetrieveAPIView,
    RetrieveUpdateAPIView,
    UpdateAPIView,
    ListCreateAPIView,
    RetrieveUpdateDestroyAPIView,
)
from rest_framework.parsers import MultiPartParser, JSONParser
from rest_framework.permissions import AllowAny
from rest_framework.renderers import JSONRenderer
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from treebeard.mp_tree import MP_Node, MP_NodeManager
from vapar.constants.conversion import StandardValueConverter
from vapar.constants.exports import ExportFormat
from vapar.constants.pipes import StandardEnum
from vapar.constants.processing import ProcessingStatusEnum
from vapar.core.exports import BulkInspectionPDFExportPayload

from api.actions.models import AuditList
from api.base.models import Header
from api.common import encrypt_url
from api.common.enums import (
    StatusEnum,
    UserLevelEnum,
    OrganisationTypeEnum,
    OperatorEnum,
    QueryFilterDataTypeEnum as FilterDataType,
)
from api.common.errors import CustomError
from api.common.json_util import MapPointLinkJSON, VideoFrameJSON
from api.common.pagination import StandardResultsSetPagination, StatusCountPagination
from api.common.permissions import (
    HasInspectionAccess,
    HasMapPointListAccess,
    HasOrganisationAccess,
    IsStandardUser,
    OrgCanUpload,
    HasAccessToOrgScopedObject,
    IsAuthenticated,
    IsProductOwner,
    IsServiceUser,
    RelatesToInspectionAccessibleByOrg,
    HasFileAccess,
    RelatesToVideoFileAccessibleByOrg,
)
from api.common.storage import (
    get_platform_blob_client,
    get_platform_blob_url_with_sas,
    get_platform_storage_sas_token,
    put_to_platform_blob_storage,
    get_processing_blob_client,
    get_processing_storage_sas_token,
    get_processing_storage_region_base_url,
)
from api.common.string_handling import camel_to_snake
from api.defects.models import DefectScores, Standard, StandardHeader
from api.exports.models import Export, ExportOutput
from api.exports.serializers import ExportSerializer
from api.exports.sync import run_exports_synchronously
from api.inspections import analytics, schemas
from api.inspections.gradings import update_gradings
from api.inspections.models import (
    Asset,
    FileList,
    ImportedInspectionFile,
    Inspection,
    InspectionFilter,
    InspectionValue,
    JobsTree,
    MapPointList,
    ProcessingList,
    VideoFrames,
)
from api.inspections.permissions import IsOrgOwnerOfProcessingFile, IsOrgUploaderOfProcessingFile
from api.inspections.pydantic_models.inspection_filter import (
    InspectionFilterEntityEnum as FilterEntity,
    InspectionSearchFilterModel,
    build_asset_value_order_subquery,
    build_inspection_value_order_subquery,
)
from api.inspections.pydantic_models.inspection_model import (
    get_inspection_representation,
    restore_cached_inspections,
    InspectionModel,
    InspectionCreateModel,
    InspectionCreateResponseModel,
)
from api.inspections.queue_trigger import enqueue_message
from api.inspections.serializers.asset_serializers import (
    AssetValueListSerializer,
)
from api.inspections.serializers.inspection_serializers import (
    FileSerializer,
    FolderSerializer,
    FramesEditSerializer,
    FramesListSerializer,
    ImportedInspectionFileSerializer,
    InspectionBulkUpdateSerializer,
    InspectionDetailSerializer,
    InspectionFilterSerializer,
    InspectionListSerializer,
    InspectionValueSerializer,
    ProcessingInspectionsListSerializer,
    InspectionValueListSerializer,
    InspectionFilePatchSerializer,
    ProcessingFileCreateSerializer,
    ProcessingFilePatchSerializer,
    VideoFrameCreateSerializer,
    InspectionSerializer,
    ProcessingFileRetrySerializer,
    FileCreateSerializer,
    InspectionFileUploadMediaSerializer,
    FrameExtendedEditSerializer,
)
from api.inspections.utilities import import_inspections
from api.inspections.utilities.data_fetching import (
    get_inspection_list,
    paginate_queryset,
    build_paginated_response_payload,
)
from api.inspections.utilities.sync_mappointlist_values import sync_to_mappointlist
from api.inspections.utilities.validate_inspections import (
    get_inspections_for_validation,
    validate_csv,
    validate_standard_value,
    validate_inspections_for_export,
)
from api.inspections.views.asset_views import clear_from_asset_cache
from api.organisations.models import AssetOwners, Organisations
from api.recommendations.models import RepairRecommendation
from api.recommendations.tasks.tasks import (
    generate_repair_recommendations,
    _process_inspections,
    run_many_repair_recommendations,
    update_or_create_recommendation,
)
from api.users.models import CustomUser

DEFAULT_PAGE_SIZE = 100

sas_cache = caches["default"]
inspection_cache = caches["inspections"]

log = logging.getLogger(__name__)


def get_status_counts(queryset: QuerySet[Inspection]) -> dict[str, int]:
    """
    Given a queryset of inspections, count the number of inspections for each status. Do not include statuses that were
    not present in the queryset.
    """
    status_counts_result = (
        queryset.filter(status__in=StatusEnum.get_status_list())
        .values("status")
        .order_by()
        .annotate(count=Count("status"))
    )
    status_counts = {r["status"]: r["count"] for r in status_counts_result}

    return status_counts


def handle_nz_direction(data, inspection: InspectionModel, use_header_names: bool):
    # Direction not a valid header for NZ. Swap value to SetupLocation.
    direction_key = "Direction" if use_header_names else "direction"
    setup_location_key = "SetupLocation" if use_header_names else "opposite of direction"

    if direction_key in data:
        updated_inspection_details = inspection.model_dump()
        updated_inspection_details.update(data)
        updated_inspection = InspectionModel.model_validate(
            updated_inspection_details,
            context={"standard": inspection.standard},
        )
        converter = StandardValueConverter(header="Direction", standard=updated_inspection.standard)
        value = converter.get_standard_value(updated_inspection.setup_location)
        data[setup_location_key] = value.value
        del data[direction_key]


class BaseInspection(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_imported_files(self, organisation: int, filter_record: InspectionFilter) -> dict:
        if filter_record:
            folder_ids = [folder["id"] for folder in filter_record.folder_filter_model]
            files = ImportedInspectionFile.objects.filter(folder__in=folder_ids)
        else:
            files = ImportedInspectionFile.objects.filter(organisation=organisation)

        # Annotate and count files by folder
        files = (
            files.values("folder")  # Group by folder ID
            .annotate(count=Count("folder"))  # Count files in each group
            .order_by("folder")
        )

        # Convert queryset to dictionary
        return {r["folder"]: r["count"] for r in files}

    def get_view_disabled(self, user: CustomUser, inspection: dict) -> dict:
        """
        Determine if the inspection is viewable by the user via inspection list.

        :param user: the requesting user
        :param inspection: the inspection to be filtered
        """

        if inspection["status"] == StatusEnum.PLANNED or not inspection["file"]:
            return {"disabled": True, "message": "This inspection has not been uploaded yet"}

        if user.is_service_user:
            return {"disabled": False, "message": ""}

        file_upload_org_type = inspection["file"]["upload_org_type"]
        user_org = user.organisation

        if (
            user_org.org_type == OrganisationTypeEnum.ASSET_OWNER
            and file_upload_org_type == OrganisationTypeEnum.CONTRACTOR
            and inspection["status"] == StatusEnum.UPLOADED
        ):
            return {
                "disabled": True,
                "message": "A contractor uploaded inspection with the status of Uploaded cannot \
                be viewed by an asset owner. Waiting for contractor review.",
            }
        return {"disabled": False, "message": ""}


def get_cached_inspections(inspection_uuids: set[str]) -> dict[str, Any]:
    """
    Retrieve any cached inspections for a given uuid set.

    :param inspection_uuids: Set of inspection ids to check against cached inspections.
    :return: Dictionary of cached inspections with inspection id as keys.
    """
    log.debug(f"start 'cache.get_many': {datetime.now()}")
    cached_inspections = inspection_cache.get_many(inspection_uuids)
    cached_inspections = restore_cached_inspections(cached_inspections)
    log.debug(f"CACHE_HITS: {len(cached_inspections)}")

    return cached_inspections


class InspectionList2QueryParams(BaseModel):
    organisation: int | None = None
    ordering: str | None = None
    search: str | None = None
    page: int = 1
    page_size: int = 25
    status: StatusEnum | None = None
    use_inspection_filters: bool = True
    date_captured: str | None = None
    asset_id: str | None = None
    folder_id: int | None = None
    file_id: int | None = None
    last_related_update__gte: str | None = None
    uuid__in: str | None = None
    use_header_names: bool = False

    model_config = {"extra": "ignore"}

    @field_validator("*", mode="before")
    @classmethod
    def coerce_lists_of_one_item(cls, val):
        """Needed because django query_params is a multidict"""
        if isinstance(val, list) and len(val) == 1:
            return val[0]
        return val


class InspectionList2(BaseInspection, ListCreateAPIView):
    queryset = Inspection.objects.all()
    pagination_class = StatusCountPagination

    search_fields = [
        ("legacy_id", FilterEntity.INSPECTION),
        ("Direction", FilterEntity.INSPECTION_VALUE),
        ("WorkOrder", FilterEntity.INSPECTION_VALUE),
        ("Material", FilterEntity.ASSET_VALUE),
        ("AssetID", FilterEntity.ASSET_VALUE),
        ("LocationStreet", FilterEntity.ASSET_VALUE),
        ("LocationTown", FilterEntity.ASSET_VALUE),
        ("UpstreamNode", FilterEntity.ASSET_VALUE),
        ("DownstreamNode", FilterEntity.ASSET_VALUE),
        ("filename", FilterEntity.FILE),
    ]

    ordering_fields = {
        "direction": ("Direction", FilterEntity.INSPECTION_VALUE, FilterDataType.STRING, None),
        "chainage": ("LengthSurveyed", FilterEntity.INSPECTION_VALUE, FilterDataType.NUMBER, 0),
        "date_captured": ("Date", FilterEntity.INSPECTION_VALUE, FilterDataType.DATE, None),
        "material": ("Material", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "asset_id": ("AssetID", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "location_street": ("LocationStreet", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "location_town": ("LocationTown", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "upstream_node": ("UpstreamNode", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "downstream_node": ("DownstreamNode", FilterEntity.ASSET_VALUE, FilterDataType.STRING, None),
        "diameter": ("HeightDiameter", FilterEntity.ASSET_VALUE, FilterDataType.NUMBER, 0),
        "filename": ("file__filename", FilterEntity.FILE, FilterDataType.STRING, None),
        "created_time": ("file__created_time", FilterEntity.FILE, FilterDataType.DATE, datetime.min),
        "id": ("legacy_id", FilterEntity.INSPECTION, FilterDataType.NUMBER, None),
        "condition_rating": ("structural_grade", FilterEntity.INSPECTION, FilterDataType.NUMBER, 1),
        "service_condition_rating": ("service_grade", FilterEntity.INSPECTION, FilterDataType.NUMBER, 1),
        "created_at": ("created_at", FilterEntity.INSPECTION, FilterDataType.DATE, datetime.min),
        "status": ("status", FilterEntity.INSPECTION, FilterDataType.STRING, None),
        "last_related_update": ("last_related_update", FilterEntity.INSPECTION, FilterDataType.DATE, datetime.min),
    }

    def get_renderers(self):
        if self.use_header_names():
            return [JSONRenderer()]
        else:
            return [CamelCaseJSONRenderer()]

    def use_header_names(self) -> bool:
        if not self.request:
            return False
        return "use_header_names" in self.request.GET and self.request.GET["use_header_names"].lower() == "true"

    def get_base_queryset(
        self, organisation: Organisations, filter_record: InspectionFilter | None
    ) -> QuerySet[Inspection]:
        qs = super().get_queryset().visible_to_org(organisation)

        if filter_record:
            folder_ids = [folder["id"] for folder in filter_record.folder_filter_model]
            query = Q(file__job_tree__in=folder_ids) | (Q(folder__in=folder_ids) & Q(status=StatusEnum.PLANNED.value))
            qs = qs.filter(query)

        qs = qs.filter(Q(legacy_id__isnull=False) | Q(status=StatusEnum.PLANNED.value))

        return qs.order_by("-legacy_id")

    def get_queryset(
        self,
        queryset: QuerySet[Inspection],
        filter_record: InspectionFilter | None,
        params: InspectionList2QueryParams,
    ):
        filters = []

        if params.status:
            queryset = queryset.filter(status=params.status.value)
        elif filter_record and (status_filters := filter_record.filter_model.get("common_filters", {}).get("status")):
            if len(status_filters["value"]) > 0:
                filters.append(
                    InspectionSearchFilterModel(
                        name="status",
                        value=status_filters["value"],
                        operator=OperatorEnum.IN,
                        filter_type=FilterEntity.INSPECTION,
                    )
                )

        if filter_record and (header_filters := filter_record.filter_model.get("header_filters")):
            filters.extend(InspectionSearchFilterModel.from_header_filter(hf) for hf in header_filters)

        if params.date_captured:
            filters.append(
                InspectionSearchFilterModel(
                    name="Date",
                    value=[params.date_captured],
                    operator=OperatorEnum.EQ,
                    filter_type=FilterEntity.INSPECTION_VALUE,
                )
            )

        if params.asset_id:
            filters.append(
                InspectionSearchFilterModel(
                    name="AssetID",
                    value=[params.asset_id],
                    operator=OperatorEnum.EQ,
                    filter_type=FilterEntity.ASSET_VALUE,
                )
            )

        for filter_condition in filters:
            queryset = queryset.filter(filter_condition.to_query())

        if params.folder_id:
            queryset = queryset.filter(folder=params.folder_id)

        if params.file_id:
            queryset = queryset.filter(file=params.file_id)

        if params.last_related_update__gte:
            queryset = queryset.filter(last_related_update__gte=params.last_related_update__gte)

        if params.uuid__in:
            uuids = params.uuid__in.split(",")
            queryset = queryset.filter(uuid__in=uuids)

        if search_term := params.search:
            search_filters = self.create_search_filters(search_term)
            search_query = Q()
            for sf in search_filters:  # Note that search filters are OR'd unlike other filters
                search_query |= sf.to_query()
            queryset = queryset.filter(search_query)

        return queryset

    def get_ordered_queryset(self, queryset: QuerySet[Inspection], ordering_field: str) -> QuerySet[Inspection]:
        if not ordering_field or ordering_field == "none":
            return queryset.order_by("-legacy_id")

        is_descending = False
        ordering = camel_to_snake(ordering_field)

        if ordering.startswith("-"):
            is_descending = True
            ordering_field = ordering_field[1:]

        if ordering_field not in self.ordering_fields:
            return queryset.order_by("-legacy_id")

        prefix = "-" if is_descending else ""
        header_name, entity, data_type, _ = self.ordering_fields[ordering_field]

        ordering_column = None
        if entity == FilterEntity.ASSET_VALUE:
            ordering_column = Subquery(build_asset_value_order_subquery(header_name, data_type))
        elif entity == FilterEntity.INSPECTION_VALUE:
            ordering_column = Subquery(build_inspection_value_order_subquery(header_name, data_type))
        else:
            ordering_column = data_type.build_type_cast_expression(header_name)

        if ordering_column is not None:
            qs = queryset.annotate(ordering_field=ordering_column).order_by(f"{prefix}ordering_field", "-legacy_id")

        return qs

    def create_search_filters(self, search_term: str) -> list[InspectionSearchFilterModel]:
        search_filters = [
            InspectionSearchFilterModel(
                name=field_name,
                filter_type=entity_type,
                data_type=FilterDataType.STRING,  # All searches done as strings
                value=[search_term],
                operator=OperatorEnum.CT,
            )
            for (field_name, entity_type) in self.search_fields
        ]

        return search_filters

    def order_inspection_list(self, inspection_list: list[InspectionModel], ordering: str) -> list[InspectionModel]:
        """
        Sort an inspection list by an ordering field

        :param inspection_list: the inspection list to be ordered
        :param ordering: the ordering required for the inspection list
        """

        def identifier_sort(item: InspectionModel):
            try:
                return int(item.id)
            except (ValueError, TypeError):
                if item.id:
                    return int("".join(i for i in item.id if i.isnumeric()))
                else:
                    return 0

        # start with an ordered list by ID descending
        inspection_list.sort(key=identifier_sort, reverse=True)

        asset_fields = ["asset_id", "location_street", "location_town", "upstream_node", "downstream_node", "diameter"]
        file_fields = ["created_time", "upload_user", "filename", "file_size"]
        folder_fields = ["folder"]

        # Set default ordering to id while merging of mpl and inspection is in progress
        ordering = "-id" if not ordering or ordering == "none" else ordering

        order_fallback_value = {
            "diameter": 0,
            "chainage": 0,
            "created_at": datetime.min,
            "created_time": datetime.min,
            "condition_rating": 1,
            "service_condition_rating": 1,
        }

        if not ordering or ordering == "none":
            return inspection_list

        is_descending = False
        ordering = camel_to_snake(ordering)

        if ordering.startswith("-"):
            is_descending = True
            ordering = ordering[1:]

        if ordering in file_fields:
            inspections_with_data = list(
                filter(
                    lambda inspection: inspection.file is not None and inspection.file[ordering] is not None,
                    inspection_list,
                )
            )
            inspection_without_data = list(
                filter(
                    lambda inspection: inspection.file is None or inspection.file[ordering] is None,
                    inspection_list,
                )
            )

            ordered_list = sorted(
                inspections_with_data,
                key=lambda inspection: inspection.file[ordering],
                reverse=is_descending,
            )

            if is_descending:
                ordered_list = ordered_list + inspection_without_data
            else:
                ordered_list = inspection_without_data + ordered_list
        elif ordering in folder_fields:
            ordered_list = sorted(
                inspection_list,
                key=lambda x: x.folder["job_name"] if x.folder is not None else "",
                reverse=is_descending,
            )
        elif ordering in asset_fields:
            fallback_value = order_fallback_value.get(ordering, "")
            ordered_list = sorted(
                inspection_list,
                key=lambda x: getattr(x.asset, ordering) if getattr(x.asset, ordering) is not None else fallback_value,
                reverse=is_descending,
            )
        elif ordering == "id":
            ordered_list = sorted(inspection_list, key=identifier_sort, reverse=is_descending)
        else:
            fallback_value = order_fallback_value.get(ordering, "")
            ordered_list = sorted(
                inspection_list,
                key=lambda x: getattr(x, ordering) if getattr(x, ordering) is not None else fallback_value,
                reverse=is_descending,
            )

        return ordered_list

    @extend_schema(
        parameters=[
            OpenApiParameter("organisation", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("ordering", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("search", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("page", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("page_size", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter(
                "status",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
            ),
            OpenApiParameter(
                "use_inspection_filters",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=True,
            ),
            OpenApiParameter("date_captured", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("asset_id", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("folder_id", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("file_id", OpenApiTypes.INT, OpenApiParameter.QUERY, required=False),
            OpenApiParameter("last_related_update__gte", OpenApiTypes.STR, OpenApiParameter.QUERY, required=False),
            OpenApiParameter(
                "uuid__in",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
                description="Comma separated list of inspection UUIDs to filter by",
            ),
            OpenApiParameter(
                "use_header_names",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="Return keys matching the OpenAPI schema field names and the header names",
            ),
        ],
        request=None,
        responses={status.HTTP_200_OK: InspectionModel},
    )
    def get(self, request, *args, **kwargs):
        try:
            query_params = InspectionList2QueryParams.model_validate(self.request.query_params)
        except pydantic.ValidationError as e:
            raise ValidationError(str(e))

        if query_params.organisation:
            organisation = get_object_or_404(Organisations, id=query_params.organisation)
            # check user has access to organisation
            if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=organisation):
                raise PermissionDenied("You do not have access to this organisation")
        else:
            organisation = self.request.organisation

        filter_record = (
            InspectionFilter.objects.filter(user=request.user, organisation=organisation).first()
            if query_params.use_inspection_filters
            else None
        )

        inspection_qs = self.get_base_queryset(organisation=organisation, filter_record=filter_record)
        inspection_qs = self.get_queryset(
            queryset=inspection_qs,
            filter_record=filter_record,
            params=query_params,
        )
        inspection_qs = self.get_ordered_queryset(inspection_qs, ordering_field=query_params.ordering)
        status_counts = get_status_counts(inspection_qs)
        inspection_count = inspection_qs.count()

        # do custom pagination
        inspection_qs = paginate_queryset(inspection_qs, query_params.page, query_params.page_size)

        inspections = get_inspection_list(inspection_qs)
        inspections = self.order_inspection_list(inspections, ordering=query_params.ordering)
        inspection_dicts = [inspection.model_dump(by_alias=query_params.use_header_names) for inspection in inspections]
        for inspection in inspection_dicts:
            inspection["view_disabled"] = super().get_view_disabled(user=request.user, inspection=inspection)

        payload = build_paginated_response_payload(
            request,
            inspection_dicts,
            count=inspection_count,
            current_page=query_params.page,
            page_size=query_params.page_size,
        )
        # Use camel-cased name so it appears the same whether or not use_header_names is set
        payload["statusCounts"] = status_counts
        return Response(payload)

    @extend_schema(
        parameters=[
            OpenApiParameter("asset_uuid", OpenApiTypes.STR, required=True),
            OpenApiParameter("folder_id", OpenApiTypes.INT, required=True),
            OpenApiParameter("file_id", OpenApiTypes.INT, required=True),
        ],
        request=OpenApiRequest(InspectionCreateModel),
        responses={status.HTTP_201_CREATED: InspectionCreateResponseModel},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create a new inspection record
        """

        # Undo the effect of camelCase -> snake_case parser on PascalCase keys
        raw_data = {re.sub(r"_([a-z])", lambda x: x.group(1).upper(), k): v for k, v in request.data.items()}
        try:
            payload = InspectionCreateModel.model_validate(raw_data)
        except pydantic.ValidationError as e:
            raise ValidationError(str(e))

        asset: Asset = get_object_or_404(Asset, uuid=request.query_params.get("asset_uuid"))
        file: FileList = get_object_or_404(FileList, id=request.query_params.get("file_id"))
        folder: JobsTree = get_object_or_404(JobsTree, id=request.query_params.get("folder_id"))

        primary_org = folder.owning_org

        # Check object permissions
        if not HasAccessToOrgScopedObject().has_object_permission(request, view=self, obj=asset):
            raise PermissionDenied("User does not have access to the asset")

        if not HasAccessToOrgScopedObject().has_object_permission(
            request, view=self, obj=file, org_field_name="target_org"
        ):
            raise PermissionDenied("User does not have access to the file")

        if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=primary_org):
            raise PermissionDenied("User does not have access to the folder")

        if asset.organisation != file.target_org or asset.organisation != primary_org:
            return Response(
                "Asset, file and folder must belong to the same organisation", status=status.HTTP_400_BAD_REQUEST
            )

        inspection = Inspection.objects.create(
            asset=asset,
            created_by=request.user,
            folder=folder,
            file=file,
            status=StatusEnum.UPLOADED,
        )

        # Invalidate the asset cache entry to keep inspections_count per asset up to date
        clear_from_asset_cache([asset.uuid])

        # Populate inspection values
        header_names_to_vals = payload.model_dump(mode="json", by_alias=True)
        standard_headers_qs = StandardHeader.objects.filter(
            standard=folder.standard_key,
            header__type=Header.HeaderType.INSPECTION,
            header__name__in=header_names_to_vals.keys(),
        ).select_related("header")
        header_names_to_standard_header = {sh.header.name: sh for sh in standard_headers_qs}
        header_names_to_vals = {k: v for k, v in header_names_to_vals.items() if k in header_names_to_standard_header}
        for header_name, val in header_names_to_vals.items():
            standard_header = header_names_to_standard_header[header_name]
            inspection.create_inspection_value(standard_header, "" if val is None else str(val))

        analytics.send_inspection_created_event(
            get_inspection_representation(inspection, skip_cache=True), inspection.asset.organisation, request.user
        )
        analytics.send_asset_linked_to_inspection_event(
            asset_id=asset.uuid, inspection_id=inspection.uuid, owner_org=asset.organisation, user=request.user
        )

        response_dto = InspectionCreateResponseModel(uuid=inspection.uuid)
        return Response(response_dto.model_dump(mode="json"), status=status.HTTP_201_CREATED)


class InspectionDetail2(BaseInspection, RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    queryset = Inspection.objects.all()
    serializer_class = InspectionSerializer
    lookup_field = "uuid"

    def get_permissions(self):
        if self.request.method == "DELETE":  # Delete is only available to service users
            return [IsAuthenticated(), IsServiceUser()]

        return super().get_permissions()

    def get_queryset(self):
        if self.request.method == "DELETE":
            return self.queryset.all()

        return self.queryset.exclude(file__hidden=True)

    def use_header_names(self) -> bool:
        if not self.request:
            return False
        return "use_header_names" in self.request.GET and self.request.GET["use_header_names"].lower() == "true"

    def get_parsers(self):
        """
        We need to preserve object keys without conversion if we are relying on mapping header names
        """
        if self.use_header_names():
            return [JSONParser()]
        else:
            return [CamelCaseJSONParser()]

    def get_renderers(self):
        if self.use_header_names():
            return [JSONRenderer()]
        else:
            return [CamelCaseJSONRenderer()]

    def handle_status_change(self, request: Request, initial_inspection: InspectionModel, mappoint: MapPointList):
        # check for running repair recommendations
        if initial_inspection.status == StatusEnum.UPLOADED.value:
            if request.data["status"] == StatusEnum.REVIEWED.value:
                # TODO: to we need to record this as an inspection value or against the inspection table directly?
                # header name: ReviewerCertificateNumber
                # then sync to MPL
                mappoint.reviewed_by = request.user.full_name
            if request.data["status"] not in [StatusEnum.PLANNED.value, StatusEnum.UPLOADED.value]:
                self.run_repair_recommendations(mappoint=mappoint)

        # sync status back to mpl table
        mappoint.status = request.data["status"]
        mappoint.save()

    def run_repair_recommendations(self, mappoint: MapPointList):
        inspections = _process_inspections([mappoint])
        generate_repair_recommendations(inspections)
        update_or_create_recommendation(inspections[0], self.request.user)

    @extend_schema(
        responses={status.HTTP_200_OK: InspectionModel},
        parameters=[
            OpenApiParameter(
                "use_header_names",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="Use header names instead of MPL field names as keys",
            ),
        ],
    )
    def get(self, request, *args, **kwargs):
        """
        Fetch an inspection record using the Pydantic model
        """
        inspection = self.get_object()
        inspection = get_inspection_representation(inspection=inspection, skip_cache=True)
        return Response(inspection.model_dump(by_alias=self.use_header_names()))

    @atomic
    @extend_schema(
        parameters=[
            OpenApiParameter("uuid", OpenApiTypes.STR, OpenApiParameter.PATH, required=True),
            OpenApiParameter(
                "use_header_names",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="Use header names instead of MPL field names as keys",
            ),
        ],
        request=dict[str, Any],
        responses={status.HTTP_200_OK: InspectionModel},
    )
    def patch(self, request, *args, **kwargs):
        """
        Update an inspection record and the associated inspection values.
        """
        data = request.data
        inspection = self.get_object()
        initial_inspection = get_inspection_representation(inspection=inspection, skip_cache=True)

        original_asset = inspection.asset
        if "asset" in data:
            asset = Asset.objects.filter(uuid=data["asset"]).first()

            if not asset:
                raise ValidationError("Unable to find an asset")

            if not HasOrganisationAccess().has_object_permission(request, view=self, organisation=asset.organisation):
                raise PermissionDenied("You do not have permission to assign an Asset for this organisation")

            if asset.standard != inspection.folder.standard_key.id:
                raise ValidationError("Asset standard must match the Inspection Standard")
        else:
            asset = inspection.asset

        if initial_inspection.standard in (
            StandardEnum.NZ_PIPE_MANUAL_3,
            StandardEnum.NZ_PIPE_MANUAL_4,
        ):
            handle_nz_direction(data, initial_inspection, self.use_header_names())

        try:
            super().patch(request, *args, **kwargs)
        except Exception as e:
            log.error(e)
            raise

        inspection = self.get_object()

        # if chainage, update gradings
        if "chainage" in data and int(initial_inspection.chainage) != int(data["chainage"]):
            update_gradings(inspection)

        inspection_repr = get_inspection_representation(inspection=inspection, skip_cache=True)

        AuditList.objects.create(
            event_type="Update",
            table="Inspection",
            description=f"Updated inspection {inspection.uuid}",
            date_of_modification=timezone.now(),
            user=request.user,
            column="Multi",
        )

        mappoint = MapPointList.objects.filter(inspection_id=inspection.uuid).last()
        if mappoint is not None:
            mapped_fields = list(request.data.keys())
            if "asset" in data:
                mapped_fields += [
                    "asset_id",
                    "upstream_node",
                    "downstream_node",
                    "material",
                    "diameter",
                    "name",
                    "pipe_type",
                ]

            sync_to_mappointlist(
                mappointlist=mappoint,
                inspection=inspection_repr,
                mapped_fields=mapped_fields,
            )

            description = f"Update survey {mappoint.id}"
            column = "Multi"

            if "status" in data:
                if len(data.keys()) == 1:
                    column = "status"
                    description = f"From value {initial_inspection.status} to value {request.data['status']}"

                self.handle_status_change(request=request, initial_inspection=initial_inspection, mappoint=mappoint)

            # update auditlist table
            AuditList.objects.create(
                event_type="Update",
                table="Mappointlist",
                row_id=mappoint.id,
                description=description,
                date_of_modification=timezone.now(),
                user=request.user,
                column=column,
            )

        analytics.send_inspection_updated_event(inspection_repr, inspection.asset.organisation, request.user)
        if asset != original_asset:
            analytics.send_asset_linked_to_inspection_event(
                asset_id=asset.uuid, inspection_id=inspection.uuid, owner_org=asset.organisation, user=request.user
            )
            # Invalidate the asset cache entry to keep inspections_count per asset up to date
            clear_from_asset_cache([asset.uuid])

        inspection_cache.delete(inspection.uuid)

        return Response(
            inspection_repr.model_dump(by_alias=self.use_header_names(), mode="json"), status=status.HTTP_200_OK
        )

    @atomic
    def delete(self, request, *args, **kwargs):
        """
        Delete an inspection and child entities
        """

        inspection: Inspection = self.get_object()
        inspection_cache.delete(str(inspection.uuid))
        RepairRecommendation.objects.filter(target__inspection=inspection).delete()
        MapPointList.objects.filter(inspection=inspection).delete()
        inspection.delete()

        # Invalidate the asset cache entry to keep inspections_count per asset up to date
        clear_from_asset_cache([inspection.asset.uuid])

        return Response(status=status.HTTP_204_NO_CONTENT)


class InspectionList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionListSerializer
    queryset = (
        MapPointList.objects.select_related("associated_file")
        .select_related("standard_key")
        .select_related("inspection")
        .filter(deleted_at__isnull=True)
        .exclude(associated_file__hidden=True)
    )
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = [
        "id",
        "asset_id",
        "name",
        "upstream_node",
        "downstream_node",
        "direction",
        "material",
        "associated_file__filename",
    ]
    ordering_fields = [
        "id",
        "status",
        "asset_id",
        "name",
        "upstream_node",
        "downstream_node",
        "direction",
        "material",
        "chainage",
        "diameter",
        "date_captured",
        "condition_rating",
        "service_condition_rating",
        "inspection_notes",
        "associated_file__filename",
        "associated_file__file_size",
        "associated_file__created_time",
        "associated_file__upload_user",
        "associated_file__job_tree__job_name",
        "associated_file__created_time",
    ]
    ordering = ["-id"]
    pagination_class = StandardResultsSetPagination

    def get_base_queryset(self, folder_ids):
        """
        The base queryset is the set of inspections after the folder filter has been applied.
        This has been separated out so that we can collect total status counts on the list of folders requested
         before we apply other filters including the status filter.
        """
        queryset = self.queryset
        folder_ids = [int(x) for x in folder_ids.split(",")]

        query_filter = (
            Q(associated_file__job_tree__in=folder_ids)
            | Q(inspection__file__job_tree__in=folder_ids)
            | (Q(inspection__folder__in=folder_ids) & Q(inspection__status=StatusEnum.PLANNED.value))
        )
        return queryset.filter(query_filter)

    def get_queryset(self, folder_ids):
        queryset = self.get_base_queryset(folder_ids=folder_ids)

        is_asset_owner = self.request.organisation.is_asset_owner
        asset_owners = AssetOwners.objects.select_related("org").filter(org=self.request.organisation)

        query_filter = Q()

        # TODO: before we get here, we should have already checked that the user has access to the folder
        if is_asset_owner and asset_owners.exists():
            # Asset owner can view inspections uploaded by their own org or their contractors (they are target_org in either case)
            query_filter &= Q(associated_file__target_org=self.request.organisation) | Q(
                inspection__asset__organisation=self.request.organisation
            )
        else:
            asset_owners = (
                AssetOwners.objects.prefetch_related("contractor")
                .select_related("org")
                .filter(contractor__org=self.request.organisation)
            )
            organisations = [asset_owner.org for asset_owner in asset_owners]
            organisations.append(self.request.organisation)
            query_filter &= Q(associated_file__upload_org=self.request.organisation) | Q(
                inspection__asset__organisation__in=organisations
            )

        status = self.request.query_params.get("status", None)
        if status is not None:
            status = status.split(",")
            for item in status:
                if item not in StatusEnum.get_status_list():
                    raise ValidationError("Invalid status")
            query_filter &= Q(status__in=status)

        return queryset.filter(query_filter)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "folder_ids",
                OpenApiTypes.STR,
                required=True,
                many=True,
                explode=False,
                description="List of job ids (folder ids)",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of inspections
        """
        # pylint: disable=protected-access
        folder_ids = request.query_params.get("folder_ids", None)
        ordering = request.query_params.get("ordering", None)

        if not folder_ids:
            raise serializers.ValidationError("You need to supply folders to retrieve inspections")

        # Generate status counts post folder filter and pre column filter
        base_queryset = self.get_base_queryset(folder_ids=folder_ids)
        status_counts_result = (
            base_queryset.filter(status__in=StatusEnum.get_status_list())
            .values("status")
            .order_by("status")
            .annotate(count=Count("status"))
        )
        status_counts = {r["status"]: r["count"] for r in status_counts_result}

        # matching
        for_matching = (
            ImportedInspectionFile.objects.select_related("folder")
            .select_related("organisation")
            .select_related("created_by")
            .filter(folder__in=[int(x) for x in folder_ids.split(",")])
            .values("folder")
            .order_by("folder")
            .annotate(count=Count("folder"))
        )

        matching_count = {r["folder"]: r["count"] for r in for_matching}

        if ordering:
            prefix = ""
            field = ordering
            find_prefix = ordering.split("-")

            if len(find_prefix) == 2:
                prefix = "-"
                field = find_prefix[1]

            if field in [
                "videoName",
                "jobName",
                "fileSize",
                "videoUser",
                "uploadedTime",
            ]:
                ordering_map = {
                    "videoName": "associated_file__filename",
                    "jobName": "associated_file__job_tree__job_name",
                    "fileSize": "associated_file__file_size",
                    "videoUser": "associated_file__upload_user",
                    "uploadedTime": "associated_file__created_time",
                }

                ordering_field = ordering_map[field]
                updated_ordering_field = prefix + ordering_field
            else:
                updated_ordering_field = re.sub("([A-Z]+)", r"_\1", ordering).lower()

            if updated_ordering_field != "id" and updated_ordering_field != "-id":
                updated_ordering_field = f"{updated_ordering_field},-id"

            request.query_params._mutable = True
            request.query_params["ordering"] = updated_ordering_field
            request.query_params._mutable = False

        # Custom implementation of super().list() - allows for serializer context
        queryset = self.filter_queryset(self.get_queryset(folder_ids=folder_ids))

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(
                page,
                many=True,
                context={"request": request, "matching_count": matching_count},
            )
            response = self.get_paginated_response(serializer.data)
            response.data["status_counts"] = status_counts
            return response

        serializer = self.get_serializer(
            queryset,
            many=True,
            context={"request": request, "matching_count": matching_count},
        )
        response = Response(serializer.data)
        response.data["status_counts"] = status_counts

        return response


class PlannedInspectionList(InspectionList):
    @extend_schema(
        parameters=[
            OpenApiParameter("folder_id", OpenApiTypes.INT, required=True),
        ]
    )
    def get(self, request, *args, **kwargs):
        folder_id = request.query_params.get("folder_id", None)

        if folder_id in [None, ""] or "," in folder_id:
            raise serializers.ValidationError("You need to supply a single folder to retrieve planned inspections")

        queryset = self.get_base_queryset(folder_ids=folder_id).filter(status="Planned").order_by("-id")
        self.paginate_queryset(queryset)
        serializer = self.get_serializer(queryset, many=True, context={"request": request})
        response = self.get_paginated_response(serializer.data)

        return response


class InspectionDetail(RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasOrganisationAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionDetailSerializer
    queryset = MapPointList.objects.filter(deleted_at__isnull=True)
    lookup_field = "id"
    http_method_names = ["get", "patch"]

    def get_object(self, pk):
        try:
            return MapPointList.objects.get(id=pk)
        except MapPointList.DoesNotExist:
            raise Http404

    def get(self, request, id=None):
        """
        Get an inspection record (mappointlist).
        """
        userobj = request.user
        is_asset_owner = userobj.is_asset_owner()

        inspection = MapPointList.objects.get(pk=id)

        if inspection.inspection:
            organisation = inspection.inspection.asset.organisation
        else:
            organisation = (
                inspection.associated_file.target_org if is_asset_owner else inspection.associated_file.upload_org
            )

        self.check_object_permissions(request, organisation)

        serializer_data = self.serializer_class(inspection)
        return Response(serializer_data.data, status=status.HTTP_200_OK)

    def patch(self, request, id=None):
        """
        Update an inspection record.
        """
        point = self.get_object(pk=id)

        data = request.data

        # location = data.get("location")
        current_status = data.get("status")
        run_repair_recommendations = False

        # audit record info
        description = f"Update survey {point.id}"
        column = "Multi"

        keys = request.data.keys()
        if current_status is not None and len(keys) == 1:
            column = "status"
            description = f"From value {point.status} to value {current_status}"

        chainage = data.get("chainage")

        if current_status is not None and point.status == "Uploaded":
            if current_status == "Reviewed":
                point.reviewed_by = request.user.full_name
            if current_status != "Uploaded":
                run_repair_recommendations = True

            point.status = current_status

        serializer = self.serializer_class(point, data=data, context={"request": request}, partial=True)
        if serializer.is_valid(raise_exception=True):
            serializer.save()

        if run_repair_recommendations:
            inspections = _process_inspections([point])
            generate_repair_recommendations(inspections)
            update_or_create_recommendation(inspections[0], request.user)

        AuditList.objects.create(
            event_type="Update",
            table="Mappointlist",
            row_id=point.id,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
            column=column,
        )

        # serializer is valid so can update the grades
        if chainage and point.chainage != chainage:
            update_gradings(point.inspection)

        return Response(serializer.data, status=status.HTTP_200_OK)


class StandardInspectionDetail(RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasMapPointListAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["get"]

    def get_object(self, id):
        try:
            return MapPointList.objects.get(id=id)
        except MapPointList.DoesNotExist:
            raise Http404

    def build_response_data(self, inspection: MapPointList) -> dict:
        inspection_values = inspection.inspection.get_inspection_values()
        inspection_value_list = InspectionValueListSerializer(inspection_values, many=True).data
        asset_values = inspection.inspection.asset.get_asset_values()
        asset_value_list = AssetValueListSerializer(asset_values, many=True).data
        for item in inspection_value_list:
            item["type"] = "inspection"
        for item in asset_value_list:
            item["type"] = "asset"

        response_data = inspection_value_list + asset_value_list

        return response_data

    @extend_schema(responses={status.HTTP_200_OK: AssetValueListSerializer(many=True)})
    def get(self, request, id=None):
        """
        GET the inspection values and asset values associated with a mappointlist through mappointlist.inspection.
        """
        inspection = self.get_object(id=id)
        self.check_object_permissions(request, inspection)

        if not inspection.inspection:
            raise ValidationError("This inspection (mappointlist) does not have an associated inspection")

        response_data = self.build_response_data(inspection)

        return Response(response_data, status=status.HTTP_200_OK)


class InspectionValueDetail(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    serializer_class = InspectionValueSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    request_schema = schemas.update_value_request_schema
    http_method_names = ["patch"]

    def get_object(self, uuid):
        try:
            return InspectionValue.objects.get(uuid=uuid)
        except InspectionValue.DoesNotExist:
            raise Http404

    def check_for_mapped_field(self, inspection: Inspection, standard_header: StandardHeader):
        """
        Checks to see if we need to sync the value to a mappointlist field.
        """
        mapped_mpl_field = standard_header.get_mapped_mpl_field()

        if mapped_mpl_field:
            mappointlist = MapPointList.objects.filter(inspection=inspection).first()
            inspection_model = get_inspection_representation(inspection=mappointlist.inspection, skip_cache=True)
            sync_to_mappointlist(
                mappointlist=mappointlist,
                inspection=inspection_model,
                mapped_fields=[mapped_mpl_field],
            )

    @extend_schema(
        request=OpenApiRequest(schemas.update_value_request_schema),
    )
    def patch(self, request, uuid):
        """
        Update a inspection value record.
        """
        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        inspection_value = self.get_object(uuid)

        self.check_object_permissions(request, inspection_value.inspection)

        data = request.data.copy()
        data["standard_header"] = inspection_value.standard_header.uuid

        serializer = self.serializer_class(
            data=data,
            instance=inspection_value,
            context={"standard_header": inspection_value.standard_header},
        )
        serializer.is_valid(raise_exception=True)
        instance = InspectionValue(**serializer.validated_data)
        validation_errors = validate_standard_value(instance)

        if len(validation_errors) > 0:
            raise ValidationError(validation_errors)

        serializer.save(context={"standard_header": inspection_value.standard_header})

        inspection = inspection_value.inspection

        if inspection_value.standard_header.header.name == "Direction":
            start_node = inspection.get_start_node_inspection_value()
            end_node = inspection.get_end_node_inspection_value()

            if start_node and end_node:
                start_node_value = str(start_node.value)
                end_node_value = str(end_node.value)

                if start_node_value and start_node_value:
                    start_node.value = end_node_value
                    start_node.save()

                    end_node.value = start_node_value
                    end_node.save()

        self.check_for_mapped_field(
            inspection=inspection_value.inspection,
            standard_header=inspection_value.standard_header,
        )

        inspection_cache.delete(inspection_value.inspection.uuid)

        return Response(serializer.data, status=status.HTTP_200_OK)


class InspectionMatch(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasMapPointListAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionDetailSerializer
    request_schema = schemas.inspection_id_schema

    def get_object(self, id):
        try:
            return MapPointList.objects.get(id=id)
        except MapPointList.DoesNotExist:
            raise Http404

    @extend_schema(
        request=OpenApiRequest(schemas.inspection_id_schema),
    )
    def patch(self, request, id):
        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        inspection = self.get_object(id)
        self.check_object_permissions(request, inspection)

        inspection_to_match_id = request.data.get("inspection_id")
        inspection_to_match = MapPointList.objects.filter(id=inspection_to_match_id).first()

        if not inspection_to_match:
            raise ValidationError("Unable to find inspection to match")

        self.check_object_permissions(request, inspection_to_match)

        if inspection_to_match.status != "Planned":
            raise ValidationError("inspection_id must have a status of Planned")

        if id == inspection_to_match_id:
            raise ValidationError("inspection_id cannot be the same as the inspection being matched")

        if inspection.get_folder_id() != inspection_to_match.get_folder_id():
            raise ValidationError("inspection_id must be in the same folder as the inspection being matched")

        # inspection to match fields need to override the inspection fields
        # unless the inspection to match field is empty.
        mapped_mpl_fields = Header.objects.filter(~Q(mapped_mpl_field="")).values_list("mapped_mpl_field", flat=True)

        for field in mapped_mpl_fields:
            if field == "opposite of direction":
                break

            if getattr(inspection_to_match, field) is not None:
                setattr(inspection, field, getattr(inspection_to_match, field))

        inspection.inspection = inspection_to_match.inspection
        inspection.save()

        inspection_to_match.soft_delete()

        description = f"Match survey {inspection.id} to survey {inspection_to_match.id}"

        AuditList.objects.create(
            event_type="Update",
            table="Mappointlist",
            row_id=inspection.id,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        serializer = self.serializer_class(inspection)
        return Response(serializer.data, status=status.HTTP_200_OK)


class FrameList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model").with_defect_score_severity().all()
    pagination_class = None

    def list(self, request, *args, **kwargs):
        """
        Get a list of VideoFrames by legacy map point list id
        """
        is_asset_owner = request.organisation.is_asset_owner
        inspection_id = kwargs["inspection_id"]

        if not inspection_id:
            raise serializers.ValidationError("You need to supply an inspection id to fetch frames")

        reported_frames = self.request.query_params.get("reported_frames")

        if reported_frames in ["False", "false", None]:
            reported_frames = False
        else:
            reported_frames = True

        # check permissions [v2 logic]
        try:
            if is_asset_owner:
                inspection = MapPointList.objects.get(
                    associated_file__target_org=request.organisation, pk=inspection_id
                )
            else:
                inspection = MapPointList.objects.get(
                    associated_file__upload_org=request.organisation, pk=inspection_id
                )
        except MapPointList.DoesNotExist:
            raise PermissionDenied({"message": "You don't have permission to access"})

        # build query
        queryset = self.get_queryset()
        query_filter = Q(parent_video__mappointlist__id=inspection_id)

        if reported_frames:
            query_filter &= Q(is_hidden=False) & (Q(defect_scores__is_shown=True))

        self.queryset = queryset.filter(query_filter).order_by("frame_id", "chainage_number")

        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        cache_key = f"{inspection_id}:{container}"
        if (sas_token := sas_cache.get(cache_key)) is None:
            sas_token = get_platform_storage_sas_token(container, region=inspection.associated_file.storage_region)
            sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)

        serializer = self.serializer_class(self.queryset, context={"sas_url": sas_token}, many=True)
        return Response(serializer.data)


class FrameList2(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, RelatesToInspectionAccessibleByOrg]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model").with_defect_score_severity().all()
    pagination_class = None

    def get_queryset(self):
        qs = self.queryset.filter(parent_video__inspection=self.kwargs.get("uuid"))

        only_show_reported = self.request.query_params.get("reported_frames", "false").lower() == "true"
        if only_show_reported:
            qs = qs.only_reported()

        return qs

    def get_serializer_context(self):
        inspection = Inspection.objects.get(uuid=self.kwargs.get("uuid"))
        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        cache_key = f"{str(inspection.uuid)}:{container}"
        if (sas_token := sas_cache.get(cache_key)) is None:
            sas_token = get_platform_storage_sas_token(container, inspection.file.storage_region)
            sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)

        context = super().get_serializer_context()
        context["sas_url"] = sas_token
        return context

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "reported_frames",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="If true, only show frames that have been reported",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of VideoFrames by inspection uuid
        """
        return super().get(request, *args, **kwargs)


class FrameListByVideo(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, RelatesToVideoFileAccessibleByOrg]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model").with_defect_score_severity().all()
    pagination_class = None

    def get_queryset(self):
        qs = self.queryset.filter(parent_video=self.kwargs.get("id"))

        only_show_reported = self.request.query_params.get("reported_frames", "false").lower() == "true"
        if only_show_reported:
            qs = qs.only_reported()

        return qs

    def get_serializer_context(self):
        video = FileList.objects.get(id=self.kwargs.get("id"))
        container = settings.BLOB_STORAGE_FRAMES_CONTAINER
        cache_key = f"{str(video.id)}:{container}"
        if (sas_token := sas_cache.get(cache_key)) is None:
            sas_token = get_platform_storage_sas_token(container, video.storage_region)
            sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)

        context = super().get_serializer_context()
        context["sas_url"] = sas_token
        return context

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "reported_frames",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="If true, only show frames that have been reported",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of VideoFrames by video id
        """
        return super().get(request, *args, **kwargs)


class FrameGlobalList(ListAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesListSerializer
    pagination_class = StandardResultsSetPagination

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ["id", "frame_id", "created_at", "updated_at"]
    ordering = ["parent_video", "frame_id"]  # default ordering is to group by video

    queryset = (
        VideoFrames.objects.prefetch_related("parent_video", "parent_video__mappointlist", "defect_model")
        .with_defect_score_severity()
        .all()
    )

    def get_queryset(self):
        if self.request.organisation.is_contractor:
            qs = (
                super()
                .get_queryset()
                .filter(
                    Q(parent_video__target_org=self.request.organisation)
                    | Q(parent_video__upload_org=self.request.organisation)
                )
            )
        else:
            qs = super().get_queryset().filter(parent_video__target_org=self.request.organisation)

        if parent_video_ids := self.request.query_params.get("parent_video__in"):
            ids = parent_video_ids.split(",")
            qs = qs.filter(parent_video__in=ids)

        if pks := self.request.query_params.get("id__in"):
            ids = pks.split(",")
            qs = qs.filter(id__in=ids)

        only_show_reported = self.request.query_params.get("reported_frames", "false").lower() == "true"
        if only_show_reported:
            qs = qs.filter(is_hidden=False)
            qs = qs.filter(defect_scores__is_shown=True)

        return qs

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "reported_frames",
                OpenApiTypes.BOOL,
                OpenApiParameter.QUERY,
                required=False,
                default=False,
                description="If true, only show frames that have been reported",
            ),
            OpenApiParameter(
                "parent_video__in",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
                description="Comma separated list of video ids to filter by",
            ),
            OpenApiParameter(
                "id__in",
                OpenApiTypes.STR,
                OpenApiParameter.QUERY,
                required=False,
                description="Comma separated list of ids to filter by",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        """
        Get a list of video frames which can span multiple videos or inspections.
        """
        return super().get(request, *args, **kwargs)


class VideoFramesCreateUpdateDestroy(CreateAPIView):
    permission_classes = [IsAuthenticated, IsServiceUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = VideoFrameCreateSerializer
    queryset = VideoFrames.objects.prefetch_related("parent_video", "defect_model", "defect_scores").all()
    pagination_class = None

    http_method_names = ["post", "delete", "patch"]

    @extend_schema(
        request=OpenApiRequest(VideoFrameCreateSerializer(many=True)),
        responses={status.HTTP_201_CREATED: VideoFrameCreateSerializer(many=True)},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create a sequence of frames, for a video, clearing any existing frames.
        """
        frames = request.data
        if not isinstance(frames, list):
            raise ValidationError("Expected body as a list")
        parent_video = get_object_or_404(FileList, id=kwargs.get("video_id"), target_org=request.organisation)
        parent_video.videoframes_set.all().delete()

        seen_ids, all_frames_serializer_data = set(), []
        for frame in frames:
            if frame["frame_id"] in seen_ids:
                raise ValidationError(f"Duplicate id provided: {frame['frame_id']}")

            seen_ids.add(frame["frame_id"])
            serializer = self.serializer_class(data=frame, context={"request": request})
            serializer.is_valid(raise_exception=True)
            new_frame = serializer.save()
            all_frames_serializer_data.append({"id": new_frame.id, **serializer.data})

        return Response(all_frames_serializer_data, status=status.HTTP_201_CREATED)

    def delete(self, request, *args, **kwargs):
        """
        Delete all frames for a video.
        """
        parent_video = get_object_or_404(FileList, id=kwargs.get("video_id"), target_org=request.organisation)
        parent_video.videoframes_set.all().delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @extend_schema(
        request=OpenApiRequest(FrameExtendedEditSerializer(many=True)),
        responses={status.HTTP_200_OK: FrameExtendedEditSerializer(many=True)},
    )
    @atomic
    def patch(self, request, *args, **kwargs):
        """
        Patch the list of given frames for a video. Frames not provided will be unm
        """
        parent_video = get_object_or_404(FileList, id=kwargs.get("video_id"), target_org=request.organisation)
        # First validate provided data itself
        FrameExtendedEditSerializer(data=request.data, many=True, partial=True, context={"request": request}).is_valid(
            raise_exception=True
        )

        ids = [f.get("id") for f in request.data]
        frames_by_id = {f.id: f for f in parent_video.videoframes_set.filter(id__in=ids)}
        if len(frames_by_id) != len(ids):
            raise ValidationError("Invalid frame ids provided")
        for frame_data in request.data:
            frame = frames_by_id[frame_data["id"]]
            # Now validate and save each frame
            serializer = self.serializer_class(
                instance=frame, data=frame_data, partial=True, context={"request": request}
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

        resp_serializer = FrameExtendedEditSerializer(
            instance=parent_video.videoframes_set.all(), many=True, context={"request": request}
        )
        return Response(resp_serializer.data)


class FrameDetail(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FramesEditSerializer
    queryset = VideoFrames.objects.with_defect_score_severity().all()

    def update(self, request, *args, **kwargs):
        """
        Update a VideoFrame record.
        """
        frame = self.get_object()
        userobj = request.user
        is_asset_owner = request.organisation.is_asset_owner

        access_denied_response = Response(
            {"error": "Access denied. Frame does not belong to this user"},
            status=status.HTTP_403_FORBIDDEN,
        )

        if is_asset_owner and not frame.parent_video.target_org == request.organisation:
            return access_denied_response

        if not is_asset_owner and not frame.parent_video.upload_org == request.organisation:
            return access_denied_response

        at_joint = request.data.get("at_joint")

        # if at joint possible = False, make sure at_joint is False
        if at_joint and frame.defect_scores.at_joint_required is False:
            request.data["at_joint"] = False

        # ensure chainage is a number
        if "chainage_number" in request.data:
            chainage_number = request.data.get("chainage_number")
            if isinstance(chainage_number, str):
                try:
                    chainage_number = float(chainage_number)
                except ValueError:
                    chainage_number = 0.0
            request.data["chainage_number"] = chainage_number
            request.data["chainage"] = chainage_number

        description = f"Frame has been updated: {frame.id}\n"

        inspection = Inspection.objects.filter(file=frame.parent_video.id).first()
        str_grade = inspection.structural_grade
        ser_grade = inspection.service_grade

        response = super().update(request, *args, **kwargs)

        if "is_hidden" in request.data:
            str_grade, ser_grade = update_gradings(inspection)
            inspection_cache.delete(inspection.uuid)

        description += "\n".join([f"{k} updated to {v}" for k, v in request.data.items()])

        now = timezone.now()
        AuditList.objects.create(
            event_type="Update",
            table="VideoFrames",
            row_id=frame.id,
            column="Multi",
            description=description,
            date_of_modification=now,
            user=userobj,
        )

        response.data["inspection_str_grade"] = str_grade
        response.data["inspection_ser_grade"] = ser_grade

        return response


class FrameDefectUpdate(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = FramesEditSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = VideoFrames.objects.all()

    @extend_schema(
        request=OpenApiRequest(
            {
                "type": "object",
                "properties": {
                    "defectId": {"type": "integer"},
                    "pageContext": {
                        "type": "object",
                        "properties": {
                            "viewMode": {"type": "string", "enum": ["all", "reported"]},
                        },
                    },
                },
                "required": ["defectId"],
            }
        ),
    )
    def patch(self, request, pk):
        """
        Update a defect on a single videoFrame
        """
        if "defect_id" not in request.data:
            raise ValidationError("No defect id provided")

        frame = get_object_or_404(VideoFrames.objects.prefetch_related("defect_scores", "parent_video"), pk=pk)
        existing_defect = frame.defect_scores

        defect = DefectScores.objects.filter(id=request.data["defect_id"]).first()

        if defect is None:
            return Response({"Error": "Defect not found"}, status=status.HTTP_404_NOT_FOUND)

        if request.user.is_asset_owner():
            if frame.parent_video.target_org_id != request.organisation.id:
                return Response(
                    {"Error": "Access denied. Please check if this frame belongs to this user."},
                    status.HTTP_403_FORBIDDEN,
                )
        else:
            if frame.parent_video.upload_org_id != request.organisation.id:
                return Response(
                    {"Error": "Access denied. Please check if this frame belongs to this user."},
                    status.HTTP_403_FORBIDDEN,
                )

        if not defect:
            return Response({"Error": "failure"}, status=status.HTTP_400_BAD_REQUEST)

        inspection = Inspection.objects.filter(file=frame.parent_video.id).first()

        # Remove non-required fields
        if not defect.clock_position_required:
            frame.at_clock = None

        if not defect.clock_spread_possible:
            frame.to_clock = None

        if not defect.at_joint_required:
            frame.at_joint = False

        new_classification = defect.defect_description
        defect_model = defect.defect_key

        if defect_model:
            new_classification = defect_model.name

        if frame.class_label != new_classification:
            description = f"Update defect from {str(frame.class_label)} to {str(defect.defect_description)}"
            if not frame.original_fixed_label:
                frame.original_fixed_label = frame.class_label
                frame.class_certainty = 0.00
            frame.class_label = new_classification
            frame.defect_model = defect_model
            frame.defect_scores = defect
            frame.fix_user = request.user.full_name

            frame.quantity1_value = defect.quantity_1_default_val
            frame.quantity2_value = defect.quantity_2_default_val
            frame.quantity1_units = defect.quantity_1_units
            frame.quantity2_units = defect.quantity_2_units

            frame.save()

            now = timezone.now()

            AuditList.objects.create(
                event_type="Update",
                table="VideoFrames",
                row_id=frame.id,
                column="Multi",
                description=description,
                date_of_modification=now,
                user=request.user,
            )

            page_context = request.data.get("page_context", {})
            view_mode = page_context.get("view_mode")
            view_mode_enum = analytics.FramesPageViewModeEnum(view_mode) if view_mode in ("all", "reported") else None

            analytics.send_frame_defect_updated_event(
                frame=frame,
                user=request.user,
                owner_org=frame.parent_video.target_org,
                updater_org=request.organisation,
                updated_at=now,
                previous_defect=existing_defect,
                new_defect=defect,
                inspection_status=inspection.status if inspection else None,
                frames_page_view_mode=view_mode_enum,
            )

        frame.is_hidden = False
        frame.save()

        str_grade, ser_grade = update_gradings(inspection)
        inspection_cache.delete(inspection.uuid)

        serializer = FramesEditSerializer(frame)
        response = serializer.data

        response["inspection_str_grade"] = str_grade
        response["inspection_ser_grade"] = ser_grade
        response["defect_class"] = defect.defect_description
        response["defect_code"] = defect.defect_code
        response["defect_id"] = defect.id

        return Response(response, status=status.HTTP_200_OK)


class FolderList(APIView):
    """
    Retrieve the folder tree for or add folder to the selected organisation.
    """

    permission_classes = [IsAuthenticated]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = FolderSerializer

    def _construct_folder_tree(self, filtered_queryset: MP_NodeManager):
        def get_anscestors_descendants():
            nodeset: Set[MP_Node] = set()
            queries: Set[str] = set()
            for node in filtered_queryset:
                node: MP_Node = node
                nodeset.add(node)
                if node.depth > 1:
                    ancs: MP_NodeManager = node.get_ancestors()  # type: ignore
                    if str(ancs.query) not in queries:
                        nodeset.update(ancs)
                    queries.add(str(ancs.query))
                if node.depth < 4:
                    desc: MP_NodeManager = node.get_descendants()  # type: ignore
                    if desc:
                        if str(desc.query) not in queries:
                            nodeset.update(desc)
                        queries.add(str(desc.query))
            return nodeset

        nodeset = get_anscestors_descendants()
        if len(nodeset) == 0:
            return []

        nodes = sorted(list(nodeset), key=lambda x: x.path)
        # obtain a list of path parts by dividing each node path into subsections of 4
        # as each row in jobs tree has a path like 000A00010001.
        # these become [[000A, 0001, 0001], ...]
        paths = [list(map("".join, zip(*[iter(node.path)] * 4))) for node in nodes]
        # use the above path list to create a branch structure.
        # NOTE: reduce applies the provided function with the last returned result
        # (or initial value - 3rd param) and the next item in the sequence.
        branches = [reduce(lambda x, y: {y: x}, path[::-1], {}) for path in paths]  # type: ignore

        def merge(first, second, path=None):
            # slightly hacky, but merges a list of dicts into a nested dict
            if path is None:
                path = []
            for key in second:
                if key in first:
                    if isinstance(first[key], dict) and isinstance(second[key], dict):
                        merge(first[key], second[key], path + [str(key)])
                    elif first[key] == second[key]:
                        pass  # same leaf value
                    else:
                        raise KeyError("Conflict at %s" % ".".join(path + [str(key)]))
                else:
                    first[key] = second[key]
            return first

        # fold the branch list down into a hierarchial data sctructure that represents
        # the folder tree. essentially merges a list of dicts.
        folder_structure = reduce(merge, branches)

        def build_tree(structure, prefix=""):
            # takes a nested dict structure and extracts data from nodeset to
            # recursively rebuilds a tree from a list of nodes
            sub_tree = []
            for path in structure:
                full_path = prefix + path
                node = next(filter(lambda x: x.path == full_path, nodeset), None)
                if node is None:
                    return sub_tree
                # remove the node to speed up subsequent lookups
                nodeset.remove(node)
                folder = {
                    "data": node.__dict__,
                    "children": build_tree(structure[path], full_path),
                    "id": node.id,
                }
                if len(folder["children"]) == 0:
                    folder.pop("children")
                sub_tree.append(folder)
            return sub_tree

        return build_tree(folder_structure)

    @extend_schema(
        parameters=[
            OpenApiParameter("organisationId", OpenApiTypes.INT, required=True, description="Organisation ID"),
            OpenApiParameter(
                "includeRoot", OpenApiTypes.BOOL, required=True, description="Include root folder in response"
            ),
        ],
        responses={status.HTTP_200_OK: dict},
    )
    def get(self, request):
        """
        Get the folder tree for an organisation.
        """
        # Validate query params
        if "organisationId" not in request.query_params:
            raise ParseError("organisationId required in query params")

        if "includeRoot" not in request.query_params:
            raise ParseError("includeRoot required in query params")

        # Get selected organisation
        try:
            organisation = Organisations.objects.get(id=request.query_params["organisationId"])
        except Organisations.DoesNotExist:
            raise NotFound("Organisation not found")

        # Get associated JobsTree
        try:
            root: MP_Node = (
                JobsTree.objects.select_related("primary_org")
                .select_related("secondary_org")
                .select_related("standard_key")
                .get(primary_org=organisation)
            )
        except JobsTree.DoesNotExist:
            raise NotFound("Root folder associated with organisation not found")

        country_code = organisation.country.code
        whole_tree = []

        if organisation == request.organisation:
            if job_name := request.query_params.get("searchQuery"):
                search_results: MP_NodeManager = root.get_tree(parent=root).filter(job_name__icontains=job_name)  # type: ignore
                whole_tree = self._construct_folder_tree(search_results)
            else:
                # get this orgs whole tree
                whole_tree = root.dump_bulk(parent=root)
        else:
            # need to check if the primary org root matching the requested org has any layers where secondary org = user org
            sub_roots: MP_NodeManager = root.get_children().filter(secondary_org=request.organisation)  # type: ignore
            for sub_root in sub_roots:
                if job_name := request.query_params.get("searchQuery"):
                    search_results = sub_root.get_tree(parent=sub_root).filter(job_name__icontains=job_name)
                    whole_tree.extend(self._construct_folder_tree(search_results))
                else:
                    whole_tree.extend(sub_root.dump_bulk(sub_root))

        if not whole_tree:
            return Response({"folders": []})

        sorted_children = (
            sorted(whole_tree[0]["children"], key=lambda d: d["data"]["job_name"])
            if whole_tree[0].get("children", False)
            else []
        )
        data = {
            "whole_tree": whole_tree,
            "sorted_folder": sorted_children,
        }
        data["country"] = country_code

        # [ ================ ]
        root_folder = whole_tree[0]
        include_root = request.query_params["includeRoot"].lower() == "true"
        standards = Standard.objects.all().values()
        standards_keys = {s["id"]: s["display_name"] for s in standards}

        def format_folder(folder, path, formatted_folders):
            # Append to path array
            current_path = path.copy()

            # Only include root if requested
            is_root = folder["id"] == root_folder["id"]
            if not is_root or (is_root and include_root):
                # Append to path array
                current_path.append(str(folder["id"]))
                # Format and append current folder
                pipe_type = "sewer" if folder["data"]["pipe_type_sewer"] else "stormwater"
                standard_key = folder["data"].get("standard_key", None)
                standard = standard_key if standard_key else folder["data"].get("standard_key", None)
                standard_name = standards_keys[standard] if standard else None

                formatted_folders.append(
                    {
                        "path": current_path,
                        "name": folder["data"]["job_name"],
                        "created_date": folder["data"]["created_date"],
                        "id": folder["id"],
                        "standard_key": standard,
                        "standard_name": standard_name,
                        "pipe_type": pipe_type,
                    }
                )

            # Recursive case
            if "children" in folder:
                # Iteratively format and append each child folder
                for child in folder["children"]:
                    format_folder(child, current_path, formatted_folders)

        # Execute formating
        formatted_folders = []
        format_folder(root_folder, [], formatted_folders)

        sorted_dict = sorted(formatted_folders, key=lambda d: d["name"].lower())

        response = {"folders": sorted_dict}

        return Response(response)

    @extend_schema(
        responses={status.HTTP_200_OK: str},  # TODO: Add response schema
    )
    def post(self, request):
        """
        Create a folder in an organisation.
        """
        # Create serializer and validate payload data
        data = request.data
        data["created_date"] = timezone.now()
        serializer = self.serializer_class(data=data, context={"request": request})
        if serializer.is_valid(raise_exception=True):
            new_folder = serializer.save()
        else:
            return Response(serializer.error_messages, status=status.HTTP_400_BAD_REQUEST)

        # [ === V2 logic === ]
        description = "Create folder -> " + new_folder.job_name
        new_folder_dump_bulk = JobsTree.dump_bulk(new_folder)
        now = timezone.now()
        AuditList.objects.create(
            event_type="Create",
            table="JobTree",
            row_id=new_folder_dump_bulk[0]["id"],
            column="Multi",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        return Response(new_folder_dump_bulk)


class FolderDetail(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = FolderSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_object(self, id):
        try:
            return JobsTree.objects.get(id=id)
        except JobsTree.DoesNotExist:
            raise Http404

    @extend_schema(
        operation_id="inspections_folder_retrieve_by_id",
    )
    def get(self, request, id):
        instance = self.get_object(id)
        if not HasOrganisationAccess.has_object_permission(
            self, request, view=self, organisation=instance.get_root().primary_org
        ):
            raise PermissionDenied("You do not have access to this folder")
        serializer = self.serializer_class(instance, context={"request": request})
        return Response(serializer.data)

    @extend_schema(responses={status.HTTP_200_OK: list[dict]})
    def patch(self, request, id):
        """
        Update a JobTree record (folder).
        """
        instance = self.get_object(id)
        serializer = self.serializer_class(instance, data=request.data, context={"request": request}, partial=True)
        serializer.is_valid(raise_exception=True)
        updated_folder = serializer.save()

        description = f"Update job: {updated_folder.job_name}"

        AuditList.objects.create(
            event_type="Update",
            table="JobTree",
            row_id=updated_folder.id,
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        updated_folder = JobsTree.dump_bulk(updated_folder)
        return Response(updated_folder)

    def delete(self, request, id):
        """
        Delete a JobTree record (folder).
        """
        instance = self.get_object(id)
        serializer = self.serializer_class(instance, data=request.data, context={"request": request}, partial=True)
        serializer.is_valid(raise_exception=True)

        instance.get_descendants().delete()
        instance.delete()

        description = "Delete a job " + instance.job_name
        now = timezone.now()
        AuditList.objects.create(
            event_type="Delete",
            table="JobTree",
            row_id=id,
            column="Multi",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        return Response({"detail": "Successfully deleted"})


class FolderFilter(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = FolderSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter("org", OpenApiTypes.INT, required=True, description="Organisation ID"),
        ],
        responses={status.HTTP_200_OK: FolderSerializer(many=True)},
    )
    def get(self, request):
        """
        Retrieve a filtered folder tree for an organisation.
        """
        # Validate query params
        if "org" not in request.query_params:
            raise ParseError("'org' query param required")

        # Get selected organisation
        try:
            organisation = Organisations.objects.get(id=request.query_params["org"])
        except Organisations.DoesNotExist:
            raise NotFound("Organisation not found")

        # Get associated JobsTree
        try:
            root = JobsTree.objects.get(primary_org=organisation)
        except JobsTree.DoesNotExist:
            raise NotFound("Root folder associated with organisation not found")

        if organisation == request.organisation:
            whole_tree = root.get_tree(parent=root)
            if job_query := request.query_params.get("query"):
                whole_tree = whole_tree.filter(job_name__icontains=job_query)
        else:
            # need to check if the primary org root matching the requested org has any layers where secondary org = user org
            sub_roots = root.get_children().filter(secondary_org=request.organisation)
            if job_query := request.query_params.get("query"):
                sub_roots = sub_roots.filter(job_name__icontains=job_query)
                sub_trees = [
                    sub_root.get_tree(sub_root).filter(job_name__icontains=job_query) for sub_root in sub_roots
                ]
            else:
                sub_trees = [sub_root.get_tree(sub_root) for sub_root in sub_roots]
            # flatten list of lists
            whole_tree = [node for sub_tree in sub_trees for node in sub_tree]

        response = [self.serializer_class(node).data for node in whole_tree]

        return Response(response)


class ProcessingFileListCreate(ListCreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ProcessingInspectionsListSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = ProcessingList.objects.all().order_by("-created_time")
    pagination_class = StandardResultsSetPagination

    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["associated_file_id"]

    def list(self, request, *args, **kwargs):
        """
        Get a list of processing files for the organisation.

        If the user is an upload only user, only files uploaded by the user are returned.
        """
        queryset = self.get_queryset()
        is_standard_user = request.user.user_level in ("standard", "Standard")

        # Filter - based on user type
        if is_standard_user:
            # Get all processing files for org if a standard user
            self.queryset = queryset.filter(
                Q(target_org=request.organisation) | Q(associated_file__upload_org=request.organisation)
            )
        else:
            # Get only my uploaded files if an upload only user
            self.queryset = queryset.filter(associated_file__uploaded_by=request.user)

        return super().list(request, *args, **kwargs)

    @extend_schema(
        request=OpenApiRequest(ProcessingFileCreateSerializer()),
    )
    def post(self, request, *args, **kwargs):
        """
        Create a ProcessingList record.

        This differs from the files/upload endpoint in that the file should already exist
        """

        serializer = ProcessingFileCreateSerializer(data=request.data, context={"request": request})
        serializer.is_valid(raise_exception=True)
        processing_record = serializer.save()

        AuditList.objects.create(
            event_type="Create",
            table="ProcessingList",
            row_id=processing_record.id,
            column="all",
            description=f"Create processing file {processing_record.filename} independent of upload",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        response_serializer = ProcessingInspectionsListSerializer(processing_record, context={"request": request})
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)


class ProcessingFileDetail(RetrieveUpdateDestroyAPIView):
    permission_classes = [IsAuthenticated, IsOrgOwnerOfProcessingFile | IsOrgUploaderOfProcessingFile]
    serializer_class = ProcessingInspectionsListSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = ProcessingList.objects.all()

    http_method_names = ["patch", "delete"]
    lookup_field = "id"

    @extend_schema(
        request=OpenApiRequest(ProcessingFilePatchSerializer),
        responses={status.HTTP_204_NO_CONTENT: None},
    )
    def patch(self, request, id):
        processing_record: ProcessingList = self.get_object()
        serializer = ProcessingFilePatchSerializer(processing_record, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        if serializer.data.get("upload_completed"):
            description = f"Processing file upload complete {processing_record.associated_file.filename}"
            AuditList.objects.create(
                event_type="Update",
                table="ProcessingList",
                row_id=processing_record.id,
                column="Multi",
                description=description,
                date_of_modification=timezone.now(),
                user=request.user,
            )

        return Response(status=status.HTTP_204_NO_CONTENT)

    def delete(self, request, id):
        """
        Delete a processing file.
        """
        instance: ProcessingList = self.get_object()

        file_exists = False
        if instance.associated_file is not None and instance.associated_file.processing_completed_time is None:
            if instance.status in (ProcessingStatusEnum.PROCESSING, ProcessingStatusEnum.STORING_RESULTS):
                instance.associated_file.hidden = True
                instance.associated_file.save()
                file_exists = True
            else:
                instance.associated_file.delete()
        instance.delete()

        if instance.associated_file:
            if instance.associated_file.job_tree:
                folder = instance.associated_file.job_tree.id
            else:
                folder = "N/A - No associated folder object"
        else:
            folder = "N/A - No associated file object"

        if file_exists and len(Inspection.objects.filter(file=instance.associated_file)) > 0:
            inspection_status = Inspection.objects.filter(file=instance.associated_file).first().status
        else:
            inspection_status = "N/A - No associated inspection object"

        if instance.status_reason:
            fail_reason = f"Fail reason: {instance.status_reason}\n"
        else:
            fail_reason = ""

        if len(CustomUser.objects.filter(full_name=instance.upload_user, organisation=instance.target_org)) > 0:
            uploaded_by = (
                CustomUser.objects.filter(full_name=instance.upload_user, organisation=instance.target_org).first().id
            )
        else:
            uploaded_by = "N/A - No associated user object"

        description = (
            "Delete a processing file:\n"
            f"File name: {instance.filename}\n"
            f"Folder: {folder}\n"
            f"Inspection status: {inspection_status}\n"
            f"Processing status: {instance.status}\n"
            f"{fail_reason}"
            f"Created date: {instance.created_time}\n"
            f"Uploaded by: {uploaded_by}\n"
        )

        AuditList.objects.create(
            event_type="Delete",
            table="JobTree",
            row_id=id,
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        response = {"detail": "Successfully deleted"}
        return Response(response)


class InspectionFileList(CreateAPIView):
    permission_classes = [IsAuthenticated, OrgCanUpload]
    serializer_class = FileSerializer

    @extend_schema(
        request=OpenApiRequest(schemas.file_upload_request_schema),
        responses={status.HTTP_202_ACCEPTED: dict},
    )
    @atomic
    def post(self, request, *args, **kwargs):
        """
        Create a FileList record.
        """
        job_id = request.data["job_id"]
        file_name = request.data["file_name"].replace("#", "")
        upload_file_name = request.data["upload_file_name"].replace("#", "")

        if not upload_file_name:
            raise ValidationError("Must send an upload file name with prepended date")

        job = JobsTree.objects.filter(id=job_id).first()

        if job is None:
            raise NotFound("Job not found")

        top_job = job.get_root()
        upload_org = request.organisation

        if not job.can_contain_inspections:
            raise PermissionDenied(
                "Cannot upload to the root folder, Recycle Bin, or folders that contain other folders"
            )

        if not job.allows_uploads_from(upload_org):
            raise PermissionDenied("Your organisation does not have permission to upload to this folder")

        target_org = top_job.primary_org
        file_url = f"{settings.BLOB_STORAGE_VIDEOS_CONTAINER}/{upload_file_name}"

        user_full_name = f"{request.user.first_name} {request.user.last_name}"
        file_size = 0

        file_data = {
            "filename": file_name,
            "file_type": "",
            "file_size": file_size,
            "url": file_url,
            "target_org": target_org.id,
            "upload_org": upload_org.id,
            "upload_user": user_full_name,
            "job_tree": job.id,
            "uploaded_by": request.user.id,
            "storage_region": target_org.country.code,
        }
        serializer = FileSerializer(data=file_data)
        serializer.is_valid(raise_exception=True)

        new_file = FileList.objects.create(**serializer.validated_data)

        description = "Created a new row into FileList table from platform user"
        now = timezone.now()

        AuditList.objects.create(
            event_type="Create",
            table="FileList",
            row_id=new_file.id,
            column="all",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        processing_file_data = {
            "filename": file_name,
            "file_size": file_size,
            "status": "Uploading",
            "created_time": str(datetime.now()),
            "upload_user": user_full_name,
            "target_org": target_org.id,
            "associated_file": new_file.id,
            "manual_qa_required": target_org.manual_qa_required,
            "sewer_data": job.pipe_type_sewer,
            "standard_key": job.standard_key.id,
            "status_reason": None,
            "times_retried": 0,
        }

        serializer = ProcessingInspectionsListSerializer(data=processing_file_data)
        serializer.is_valid(raise_exception=True)

        processing_file = ProcessingList.objects.create(**serializer.validated_data)

        description = "Created a new row into ProcessingList table from platform user"
        now = timezone.now()

        AuditList.objects.create(
            event_type="Create",
            table="ProcessingList",
            row_id=processing_file.id,
            column="all",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        blob_location = request.data.get("location", "")

        if blob_location:
            container = blob_location[: blob_location.find("/")]
        else:
            container = settings.BLOB_STORAGE_VIDEOS_CONTAINER

        sas_token = get_processing_storage_sas_token(container, write_access=True, region=target_org.country.code)
        base_url = get_processing_storage_region_base_url(region=target_org.country.code)
        base_url_with_sas = f"{base_url}?{sas_token}"

        log.info(f"Using container: {container}")
        log.info(f"Using base url with sas: {base_url_with_sas}")

        return Response(
            {
                "file_id": new_file.id,
                "processing_file_id": processing_file.id,
                "blob_url_with_sas": base_url_with_sas,
                "upload_file_name": upload_file_name,
            },
            status=status.HTTP_202_ACCEPTED,
        )


class InspectionFileUploadComplete(UpdateAPIView):
    permission_classes = [IsAuthenticated, OrgCanUpload]
    queryset = FileList.objects.filter(hidden=False)
    http_method_names = ["patch"]

    @extend_schema(
        request=None,
        responses={status.HTTP_200_OK: dict},
    )
    def patch(self, request, pk):
        """
        Signal completion of upload. Blocks until the file is found in storage.
        """

        # This function is essentially a no-op.
        # The status change and enqueueing should be handled by the file blob trigger.

        # At least verify the file record exists and the user has access to it
        _file_object: FileList = self.get_object()
        return Response(
            {"success": "Upload is complete. The file has been queued for processing"},
            status=status.HTTP_200_OK,
        )


class FileDownload(RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter("inspectionId", OpenApiTypes.INT, required=True),
        ],
        responses={status.HTTP_200_OK: schemas.file_download_response_schema},
    )
    def get(self, request, *args, **kwargs):
        """
        Get the download details for a FileList record.
        """
        inspection_id = request.query_params.get("inspectionId")

        try:
            uuid.UUID(inspection_id, version=4)
            inspection = get_object_or_404(Inspection.objects.all(), uuid=inspection_id)
        except ValueError:
            inspection = get_object_or_404(Inspection.objects.all(), legacy_id=inspection_id)

        if (file := inspection.file) is not None:
            if (
                file.target_org == request.organisation
                or file.upload_org == request.organisation
                or inspection.file.uploaded_by == request.user
            ):
                cache_key = f"{file.id}:{settings.BLOB_STORAGE_VIDEOS_CONTAINER}"
                if (sas_token := sas_cache.get(cache_key)) is None:
                    sas_token = get_platform_storage_sas_token(
                        container_name=settings.BLOB_STORAGE_VIDEOS_CONTAINER, region=file.storage_region
                    )
                    sas_cache.set(cache_key, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)
                resp = {
                    "name": file.filename,
                    "url": get_platform_blob_url_with_sas(
                        blob_path=file.url, sas_token=sas_token, region=file.storage_region
                    ),
                    "play": get_platform_blob_url_with_sas(
                        blob_path=file.play_url, sas_token=sas_token, region=file.storage_region
                    ),
                }
                return Response(resp, status=status.HTTP_200_OK)

            raise PermissionDenied("Access Denied. User does not have access to this file.")

        return Response(None, status=status.HTTP_404_NOT_FOUND)


class InspectionBulkMove(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_folder(self, id):
        try:
            return JobsTree.objects.get(id=id)
        except JobsTree.DoesNotExist:
            raise NotFound("Folder not found (id:{})".format(id))

    @extend_schema(
        request=OpenApiRequest(schemas.base_bulk_inspection_request_schema),
        responses={status.HTTP_200_OK: str},
    )
    def patch(self, request, folder_id):
        """
        Bulk move inspections to a folder. Id is referring to the target folder id.
        Planned inspections can't be moved as they don't have an associated file.
        """
        destination_folder_id = folder_id
        destination_folder = self.get_folder(destination_folder_id)

        # Business level - permission checking
        if not destination_folder.can_contain_inspections or destination_folder.secondary_org:
            raise PermissionDenied(
                "Cannot move files (inspections) into the root folder or folders that contain other folders"
            )

        # TODO: check if the user has permission to move files into the destination folder
        # TODO: check if the user has access to the files to be moved

        # Moving by inspection id list
        if request.data.get("inspection_ids", None) is not None:
            inspections = Inspection.objects.filter(uuid__in=request.data["inspection_ids"])

            # check all inspections have an associated file
            if inspections.filter(file__isnull=True).exists():
                raise PermissionDenied("Moving inspections without an associated file is not supported")

            inspections = (
                Inspection.objects.filter(uuid__in=request.data["inspection_ids"])
                .filter(Q(file__target_org=request.organisation) | Q(file__upload_org=request.organisation))
                .select_related("file__job_tree")
            )

            if len(inspections) == 0:
                raise NotFound("Inspections specified to be moved were not found")

            distinct_inspections = (
                inspections.distinct("file__job_tree").order_by("file__job_tree").select_related("file__job_tree")
            )

            if distinct_inspections.count() > 1:
                # Check if the folders have the same ancestor
                secondary_org = []

                for inspection in distinct_inspections:
                    folder = inspection.file.job_tree
                    sec_org = folder.get_ancestors().exclude(secondary_org=None)
                    if sec_org:
                        sec_org = sec_org.first().secondary_org.id
                    else:
                        sec_org = None

                    if len(secondary_org) == 0:
                        secondary_org.append(sec_org)
                    elif sec_org != secondary_org[0]:
                        return PermissionDenied("Cannot move files (inspections) between organisations")

            current_folder = inspections[0].file.job_tree

        # Moving by current folder
        elif request.data.get("current_folder_id", None) is not None:
            current_folder = self.get_folder(request.data["current_folder_id"])
            inspections = (
                Inspection.objects.filter(file__job_tree=current_folder)
                .filter(Q(file__target_org=request.organisation) | Q(file__upload_org=request.organisation))
                .select_related("file__job_tree")
            )
            if len(inspections) == 0:
                raise NotFound("Inspections specified to be moved were not found")

        # No files specified
        else:
            raise ParseError("inspectionIds or currentFolderId required")

        # If destination is contractor, check if current is the same contractor
        # otherwise if AO, they will both be null.
        current_folder_second = current_folder.get_ancestors().exclude(secondary_org=None)
        destination_folder_second = destination_folder.get_ancestors().exclude(secondary_org=None)
        if current_folder_second or destination_folder_second:
            destination_secondary = None
            current_secondary = None
            if destination_folder_second:
                destination_secondary = destination_folder_second.first().secondary_org.id
            if current_folder_second:
                current_secondary = current_folder_second.first().secondary_org.id
            if current_secondary != destination_secondary:
                raise PermissionDenied("Cannot move files (inspections) between the top level folders for contractors.")

        elif current_folder.get_root() != destination_folder.get_root():
            raise PermissionDenied("Cannot move files between organisations")

        # Perform bulk update
        file_bulk_update_list = []
        inspection_bulk_update_list = []

        for inspection in inspections:
            inspection_cache.delete(str(inspection.uuid))
            original_folder = inspection.file.job_tree

            original_folder_org = original_folder.get_root().primary_org
            secondary_folder = original_folder.get_ancestors().exclude(secondary_org=None).first()
            secondary_folder_org = None
            if secondary_folder:
                secondary_folder_org = secondary_folder.secondary_org.id

            # Check original and destination folder belongs to this user org
            if original_folder_org.id != request.organisation.id and secondary_folder_org != request.organisation.id:
                raise PermissionDenied({"message": "You don't have permission to move the files from this folder"})

            inspection.file.job_tree = destination_folder
            inspection.folder = destination_folder
            inspection_bulk_update_list.append(inspection)
            file_bulk_update_list.append(inspection.file)

            description = "From value " + str(original_folder.id) + " to value " + str(destination_folder.id)
            now = timezone.now()
            AuditList.objects.create(
                event_type="Update",
                table="Inspection",
                row_id=inspection.legacy_id,
                column="file.job_tree",
                description=description,
                date_of_modification=now,
                user=request.user,
            )

        if len(file_bulk_update_list) > 0:
            FileList.objects.bulk_update(file_bulk_update_list, ["job_tree"])

        if len(inspection_bulk_update_list) > 0:
            Inspection.objects.bulk_update(inspection_bulk_update_list, ["folder"])

        return Response("Moved successfully")


class ImportedInspectionFileList(CreateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasOrganisationAccess]
    parser_classes = [MultiPartParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = ImportedInspectionFileSerializer
    queryset = ImportedInspectionFile.objects.all()

    @atomic
    def post(self, request, *args, **kwargs):
        """
        Import a csv for creation of planned inspections
        """
        folder = request.data.get("folder", None)
        file = request.FILES["file"]

        folder_obj = None

        if folder:
            folder_obj = JobsTree.objects.filter(id=folder).first()

        if not folder:
            raise ValidationError("Must provide valid folder")

        if not file or file.content_type != "text/csv":
            raise ValidationError("Must provide a csv file")

        top_folder = folder_obj.get_root()

        if not HasOrganisationAccess.has_object_permission(
            self, request, view=self, organisation=top_folder.primary_org
        ):
            raise PermissionDenied("You do not have access to this folder")

        standard = folder_obj.standard_key
        standard_name = standard.display_name

        csv_data = pd.read_csv(file, keep_default_na=False, na_values=[""])
        csv_data.fillna("", inplace=True)
        csv_data.drop_duplicates(keep="first")

        errors = validate_csv(csv_data=csv_data, standard=standard, folder=folder)

        duplicates = None
        duplicate_rows = []

        if errors:
            error_messages = []
            header_errors = errors["header_errors"]
            data_errors = errors["data_errors"]

            duplicates = errors.get("duplicates", False)
            duplicate_rows = errors.get("duplicate_rows")

            if header_errors:
                if len(errors["missing_required_headers"]) > 0:
                    error_messages.append(f"CSV is missing required headers for {standard_name}.")
                    err_str = ", ".join(map(str, errors["missing_required_headers"]))
                    error_messages.append(f"Missing headers: {err_str}.")

                if len(errors["invalid_headers"]) > 0:
                    error_messages.append(f"CSV contains headers that are not valid for {standard_name}.")
                    err_str = ", ".join(map(str, errors["invalid_headers"]))
                    error_messages.append(f"Invalid headers: {err_str}.")

            if data_errors:
                if len(errors["missing_required_data"]) > 0:
                    error_messages.append(f"CSV is missing required data for {standard_name}.")
                    for entry in errors["missing_required_data"]:
                        data_type_str = entry["required_data_type"]
                        if entry["required_data_type"] == "options":
                            data_type_str = f"one of the following: {entry['options']}"
                        error_messages.append(
                            f"Missing data for {entry['header']} on row {entry['row']}. Expected data type is {data_type_str}."
                        )

                if len(errors["invalid_data"]) > 0:
                    error_messages.append(f"CSV contains invalid data for {standard_name}.")
                    for entry in errors["invalid_data"]:
                        data_type_str = entry["required_data_type"]
                        if entry["required_data_type"] == "options":
                            data_type_str = f"one of the following: {entry['options']}"
                        error_messages.append(
                            f"Invalid data for {entry['header']} with value {entry['value']} on row {entry['row']}. Expected data type is {data_type_str}."
                        )

            if header_errors or data_errors:
                return Response(
                    {
                        "inspections_imported": False,
                        "error_messages": error_messages,
                        **errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

        request.data["created_by"] = request.user.id
        request.data["organisation"] = top_folder.primary_org_id
        request.data["file_name"] = file.name

        response = super().post(request, *args, **kwargs)

        file_bytes = file.file.getvalue()
        put_to_platform_blob_storage(
            file=file_bytes,
            blob_name=f"{response.data['uuid']}.csv",
            container_name=settings.BLOB_STORAGE_DRT_CONTAINER,
        )

        file_obj = ImportedInspectionFile.objects.filter(uuid=response.data["uuid"]).first()

        if duplicate_rows:
            file_obj.imported = False
            file_obj.import_action = "awaiting_action"
            file_obj.save()

            response.data = {
                "inspections_imported": False,
                "data": {**response.data},
                "warnings": {
                    "duplicates": duplicates,
                    "duplicate_rows": duplicate_rows,
                },
            }
            return response

        import_inspections.create_inspection_records(
            csv_data=csv_data,
            standard=standard,
            folder=folder_obj,
            inspection_file=response.data["uuid"],
            user=request.user,
            organisation=request.organisation,
        )

        file_obj.imported = True
        file_obj.save()

        description = f"Inspections CSV uploaded by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="Create",
            table="InspectionFile",
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        serializer = self.serializer_class(file_obj)

        response.data = {
            "inspections_imported": True,
            "data": {**serializer.data},
        }

        return response


class ImportedInspectionFileDetail(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = ImportedInspectionFile.objects.all()
    serializer_class = ImportedInspectionFileSerializer

    @atomic
    def post(self, request, uuid):
        """
        Process uploaded inspections file to create inspection records.
        """
        file = ImportedInspectionFile.objects.filter(uuid=uuid).first()
        if not file:
            raise NotFound("Imported inspections file not found")

        if file.imported:
            raise ValidationError("This file has already been imported")

        top_folder = file.folder.get_root()

        if not HasOrganisationAccess.has_object_permission(
            self, request, view=self, organisation=top_folder.primary_org
        ):
            raise PermissionDenied("You do not have access to this folder")

        action = request.data.get("action", None)

        if not action:
            raise ValidationError("Must provide an action for processing duplicate imported inspections")

        inspections = InspectionValue.objects.filter(imported_inspection_file=file.uuid).first()
        if inspections:
            raise ValidationError("This file has already been imported. Please upload a new file")

        blob = None

        try:
            blob = get_platform_blob_client(
                blob_name=f"{file.uuid}.csv", container_name=settings.BLOB_STORAGE_DRT_CONTAINER
            )
            if not blob.exists():
                raise ValidationError("Unable to locate csv file")
        except Exception:
            raise ValidationError("Unable to locate csv file")

        action_options = [
            "cancel_import",
            "create_duplicates",
            "overwrite_duplicates",
            "ignore_duplicates",
        ]

        if action not in action_options:
            raise ValidationError(f"Action must be one of the following: {action_options.join(',')}")

        if action == "cancel_import":
            file.delete()
            msg = "Import has been cancelled"
        else:
            blob_content = blob.download_blob()
            csv_data = pd.read_csv(StringIO(blob_content.content_as_text()))
            csv_data = csv_data.fillna("")
            csv_data.drop_duplicates(keep="first")
            standard = file.folder.standard_key
            user = CustomUser.objects.filter(id=request.user.id).first()

            data = {
                "csv_data": csv_data,
                "standard": standard,
                "folder": file.folder,
                "inspection_file": file.uuid,
                "user": user,
                "organisation": file.organisation,
                "action": action,
            }

            msg = "Successfully imported inspections"

            import_inspections.create_inspection_records(**data)
            file.imported = True
            file.save()

        description = f"Inspection File processed into DB by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="Create",
            table="InspectionFile",
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(msg, status=status.HTTP_200_OK)


class BulkUpdateStatus(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionBulkUpdateSerializer
    request_schema = schemas.bulk_update_status_request_schema

    @extend_schema(
        request=OpenApiRequest(schemas.bulk_update_status_request_schema),
        responses={status.HTTP_200_OK: InspectionSerializer(many=True)},
    )
    @atomic
    def post(self, request):
        """
        Bulk update inspection status.
        Planned inspections cannot be updated in bulk.
        """
        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        # Set vars
        is_asset_owner = request.organisation.is_asset_owner
        inspection_ids = request.data["inspection_ids"]
        status = request.data["status"]

        serializer_data = {"status": status}

        base_inspections = Inspection.objects.filter(pk__in=inspection_ids)

        if base_inspections.filter(file__isnull=True).exists():
            raise NotFound("You cannot update the status of an inspection without an associated file.")

        inspections = base_inspections.filter(
            Q(file__target_org=request.organisation) | Q(file__upload_org=request.organisation)
        )
        if len(inspections) == 0:
            raise NotFound("No inspections returned (Check inspection ids and users permissions)")

        # Count related contractors
        asset_owner_exists = AssetOwners.objects.filter(org=request.organisation).exists()
        related_contractor_count = 0
        if is_asset_owner and asset_owner_exists:
            related_contractor_count = request.organisation.assetowners.contractor.count()

        # Setup serializer and validate data
        serializer = self.serializer_class(data=serializer_data, partial=True)
        serializer.is_valid(raise_exception=True)
        mpl_bulk_update_list = []
        inspection_bulk_update_list = []

        invalid_ids = []
        if serializer.validated_data["status"] == StatusEnum.UPLOADED and related_contractor_count > 0:
            # If asset_owner has contractor and the points from uploaded belong to them
            # Record those points, and exclude them from saving
            related_contractors = request.organisation.assetowners.contractor.all()
            contractor_inspections = inspections.filter(file__upload_org__contractors__in=related_contractors)
            if len(contractor_inspections) > 0:
                inspections = inspections.exclude(uuid__in=contractor_inspections)
                if len(inspections) == 0:
                    raise PermissionDenied("Cannot move inspections uploaded by contractors")
                else:
                    invalid_ids = list(contractor_inspections.values_list("id", flat=True))

        elif serializer.validated_data["status"] != StatusEnum.UPLOADED and related_contractor_count > 0:
            # if move to other status, exclude points from contractor uploaded where original status is uploaded
            related_contractors = request.organisation.assetowners.contractor.all()
            inspections = inspections.exclude(
                file__upload_org__contractors__in=related_contractors,
                status=StatusEnum.UPLOADED,
            )
            if len(inspections) == 0:
                raise PermissionDenied("Cannot move inspections uploaded by contractors")
        rr_run = []
        now = timezone.now()
        for inspection in inspections:
            original_status = inspection.status
            inspection.status = serializer.validated_data["status"]
            inspection.last_related_update = now

            if original_status == StatusEnum.UPLOADED and inspection.status != original_status:
                rr_run.append(inspection.legacy_id)

            mappoint = MapPointList.objects.get(inspection_id=inspection.uuid)
            mappoint.status = serializer.validated_data["status"]
            if serializer.validated_data["status"] == StatusEnum.REVIEWED and original_status == StatusEnum.UPLOADED:
                mappoint.reviewed_by = request.user.full_name
                # TODO this needs to be recorded on the inspection record as well as MPL
                proc_obj = ProcessingList.objects.filter(associated_file=inspection.file).first()
                video_id = inspection.file.id
                if proc_obj:
                    if proc_obj.request_endpoint != "":
                        payload = {
                            "video_id": video_id,
                            "progress": "COMPLETE",
                            "results": {
                                "Asset Data": MapPointLinkJSON(mappoint, request.organisation),
                                "Frame Data": [
                                    VideoFrameJSON(frame)
                                    for frame in VideoFrames.objects.prefetch_related("parent_video", "defect_scores")
                                    .filter(parent_video__pk=video_id)
                                    .filter(is_hidden=False)
                                    .exclude(defect_scores__is_shown=False)
                                ],
                            },
                        }
                        requests.post(proc_obj.request_endpoint, body=payload)
                    proc_obj.delete()
                objs = VideoFrames.objects.filter(parent_video__id=video_id).filter(is_accepted=False)
                for obj in objs:
                    obj.is_accepted = True
                    obj.save()
            mpl_bulk_update_list.append(mappoint)
            inspection_bulk_update_list.append(inspection)
            description = "From value " + str(original_status) + " to value " + serializer.validated_data["status"]
            AuditList.objects.create(
                event_type="Update",
                table="MapPointList",
                row_id=mappoint.id,
                column="status",
                description=description,
                date_of_modification=now,
                user=request.user,
            )

        MapPointList.objects.bulk_update(mpl_bulk_update_list, ["status", "reviewed_by"])
        Inspection.objects.bulk_update(inspection_bulk_update_list, ["status", "last_related_update"])

        run_many_repair_recommendations(rr_run, request.user)

        description = f"Bulk update status to {status} requested by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="Export",
            table="MapPointList",
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        insp_ids = list(str(insp.uuid) for insp in inspections)
        inspection_cache.delete_many(insp_ids)

        if len(invalid_ids) > 0:
            return Response({"status": "Partially updated. Invalid ids: " + str(invalid_ids)})
        else:
            return Response(
                [InspectionSerializer(inspection).data for inspection in inspections],
                status=200,
            )


class BulkDeleteFiles(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    request_schema = schemas.base_bulk_inspection_request_schema

    def update_audit_list(self, request, description: str, table: str) -> None:
        """
        Update the audit list with the export event.
        """
        AuditList.objects.create(
            event_type="Bulk delete",
            table=table,
            column="Multi",
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

    # OpenApi3 doesn't permit body on DELETE requests so the schema on this is not generated correctly
    @extend_schema(
        request=OpenApiRequest(schemas.base_bulk_inspection_request_schema),
        responses={status.HTTP_200_OK: str},
    )
    def delete(self, request):
        """
        Bulk delete FileList records
        """

        try:
            jsonschema.validate(instance=request.data, schema=self.request_schema)
        except jsonschema.ValidationError as error:
            raise ParseError(error.message)

        inspection_ids = request.data["inspection_ids"]
        inspections = Inspection.objects.filter(uuid__in=inspection_ids)

        for inspection in inspections:
            self.check_object_permissions(request, inspection)
            inspection_cache.delete(inspection.uuid)

            if inspection.file:
                processing_file = ProcessingList.objects.filter(associated_file=inspection.file).first()

                if processing_file:
                    processing_file_id = processing_file.id
                    processing_file.delete()
                    inspection.associated_file.delete()

                    processing_description = f"Processing file {processing_file_id} was deleted."
                    self.update_audit_list(request, processing_description, "ProcessingList")
                else:
                    inspection.file.hidden = True
                    inspection.file.save()

                filelist_description = f"File {inspection.file.id} was deleted."
                self.update_audit_list(request, filelist_description, "FileList")

            else:
                # if the record does not have an associated file, delete it
                inspection_description = f"Inspection {inspection.uuid} was deleted."
                inspection.delete()
                self.update_audit_list(request, inspection_description, "Inspection")

        return Response("success")


class BulkInspectionValidation(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter("inspection_ids", OpenApiTypes.UUID, required=True, many=True, explode=False),
        ],
        responses={status.HTTP_200_OK: schemas.bulk_validation_response_schema},
    )
    def get(self, request):
        """
        Get validation results for a list of inspections
        """

        if "inspection_ids" not in request.query_params:
            raise ParseError("inspection_ids is a required query parameter")

        inspection_uuids = request.query_params["inspection_ids"].split(",")

        inspections_qs = get_inspections_for_validation(inspection_uuids)
        validation = validate_inspections_for_export(inspections_qs)

        return Response(validation)


class ShareableVideoPlayLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = Inspection.objects.all()
    schema = None

    def get(self, request, inspection_id=None):
        """
        Get an external shareable link to play a video
        """
        if request.GET.get("q", "") == encrypt_url.getmyshacode(inspection_id):
            try:
                inspection = self.get_queryset().get(legacy_id=inspection_id)
            except Inspection.DoesNotExist:
                try:
                    inspection = self.get_queryset().get(uuid=inspection_id)
                except Exception:
                    return Http404()
            url = get_platform_blob_url_with_sas(
                blob_path=inspection.file.play_url, region=inspection.file.storage_region
            )
            return HttpResponseRedirect(redirect_to=url)
        else:
            return JsonResponse({"error": "Action not allowed"})


class ShareableVideoDownloadLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = FileList.objects.all()
    schema = None

    def get(self, request, token=None):
        """
        Get an external shareable link to download a video
        """
        password = settings.IMAGE_SECRET_KEY
        salt = settings.SALT
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        f = Fernet(key)
        token = bytes(token, encoding="utf8")
        try:
            id, org = f.decrypt(token).decode("utf-8").split(":")
        except Exception:
            raise Http404

        video = get_object_or_404(self.get_queryset(), pk=id, target_org=org)
        video_url = get_platform_blob_url_with_sas(blob_path=video.url, region=video.storage_region)

        description = "Shareable video download link accessed externally"

        AuditList.objects.create(
            event_type="GET",
            table="FileList",
            column="Multi",
            row_id=id,
            description=description,
            date_of_modification=timezone.now(),
        )

        return HttpResponseRedirect(video_url)


class ShareableImageLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = VideoFrames.objects.all()
    schema = None

    def get(self, request, token=None):
        """
        Get an external shareable link to download an image
        """
        # Symetric encryption -- decrypt using keys from settings
        password = settings.IMAGE_SECRET_KEY
        salt = settings.SALT
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        f = Fernet(key)
        token = bytes(token, encoding="utf8")

        try:
            uid, org = f.decrypt(token).decode("utf-8").split(":")
        except Exception:
            raise Http404

        frame = get_object_or_404(self.get_queryset(), pk=uid, parent_video__target_org=org)

        container, blob_name = frame.image_location.split("/", 1)
        blob_client = get_platform_blob_client(
            blob_name=blob_name, container_name=container, region=frame.parent_video.storage_region
        )
        requested_image = blob_client.download_blob().readall()

        response = HttpResponse(requested_image, content_type="image/png")
        response["Content-Disposition"] = "in-line"

        description = "Shareable image link accessed externally"

        AuditList.objects.create(
            event_type="GET",
            table="VideoFrame",
            column="Multi",
            row_id=uid,
            description=description,
            date_of_modification=timezone.now(),
        )

        return response


class ShareablePDFLink(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    schema = None

    def get(self, request, token=None):
        """
        Get an external shareable link to download a PDF
        """

        # Symetric encryption -- decrypt using keys from settings
        password = settings.IMAGE_SECRET_KEY
        salt = settings.SALT
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        f = Fernet(key)
        token = bytes(token, encoding="utf8")
        # NOTE: unsure of where below was used.
        # domain = request.build_absolute_uri("/")[:-1]

        try:
            vapar_id, org = f.decrypt(token).decode("utf-8").split(":")
        except Exception:
            raise Http404

        # Set org and user to best available values
        request.organisation = Organisations.objects.get(pk=org)
        request.user = CustomUser.objects.filter(organisation=org, is_active=True).first()

        inspection = Inspection.objects.get(legacy_id=vapar_id)

        export_payload = {
            "payload": BulkInspectionPDFExportPayload(
                inspection_ids=[inspection.uuid],
                format=ExportFormat.PDF,
            ).model_dump(mode="json", by_alias=True),
        }
        serializer = ExportSerializer(
            data=export_payload,
            context={"request": request, "user": request.user, "is_initiated_by_user": False},
        )
        serializer.is_valid(raise_exception=True)
        export: Export = serializer.save()

        AuditList.objects.create(
            event_type="Export",
            table="Export",
            column="Multi",
            description=f"1 exports created for organisation {org}",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        successful_exports, failed_exports = run_exports_synchronously(
            exports=[export],
            org=request.organisation,
            max_poll_time_secs=settings.EXTERNAL_INSPECTIONS_PDF_VIEW_TIMEOUT_SECS,
        )
        if failed_exports:
            return Response("Failed to create all exports", status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        successful_export = successful_exports[0]
        export_output = ExportOutput.objects.get(export=successful_export.id)
        sas_url = get_platform_blob_url_with_sas(export_output.blob_url, region=request.organisation.country)
        response = HttpResponseRedirect(sas_url)

        description = "Shareable PDF link accessed externally"

        AuditList.objects.create(
            event_type="GET",
            table="Inspection",
            column="Multi",
            row_id=vapar_id,
            description=description,
            date_of_modification=timezone.now(),
        )
        return response


class InspectionShareableLinks(RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(responses={status.HTTP_200_OK: dict})
    def get(self, request, id: int):
        """
        Get external shareable links for a given inspection
        """
        inspection = MapPointList.objects.get(id=id)
        vid_uid = encrypt_url.encrypt(inspection.associated_file.id, inspection.associated_file.target_org.id)
        vid_url = encrypt_url.build_encrypted_url(settings.VIDEO_DOMAIN, vid_uid)

        is_ao_exist = AssetOwners.objects.filter(org=request.organisation).exists()

        if is_ao_exist:
            related_contractor_count = request.organisation.assetowners.contractor.count()
            all_related_contractors = request.organisation.assetowners.contractor.all()
            all_contractor_orgs = Organisations.objects.filter(contractors__in=all_related_contractors).values_list(
                "id", flat=True
            )
        else:
            related_contractor_count = 0

        # if the file status in uploaded and the login user is assetowner who wants to access contractor's files
        # we disable the pdf url
        if (
            related_contractor_count > 0
            and inspection.status == "Uploaded"
            and inspection.associated_file.upload_org.id in all_contractor_orgs
        ):
            pdf_url = ""
        else:
            pdf_uid = encrypt_url.encrypt(inspection.id, inspection.associated_file.target_org.id)
            pdf_url = encrypt_url.build_encrypted_url(settings.PDF_DOMAIN, pdf_uid)

        data = {"video_link": vid_url, "pdf_link": pdf_url}

        description = f"Shareable links requested by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="GET",
            table="MappointList",
            column="Multi",
            row_id=id,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(data, status=status.HTTP_200_OK)


class InspectionFilterDetail(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_organisation(self, organisation_id):
        try:
            organisation = Organisations.objects.get(id=organisation_id)
        except Organisations.DoesNotExist:
            raise NotFound(CustomError(code="ORGANISATION_NOT_FOUND", message="Organisation not found").serialize())
        return organisation

    @extend_schema(
        parameters=[
            OpenApiParameter("organisation", OpenApiTypes.INT, required=True, description="Organisation ID"),
        ],
        responses={status.HTTP_200_OK: schemas.inspection_filter_response_schema},
    )
    def get(self, request):
        organisation = self.get_organisation(request.GET.get("organisation"))

        try:
            inspection_filter = InspectionFilter.objects.get(organisation=organisation, user=request.user)
            serializer = InspectionFilterSerializer(inspection_filter)
            return Response(serializer.data)
        except InspectionFilter.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @extend_schema(
        request=OpenApiRequest(schemas.inspection_filter_request_schema),
        responses={status.HTTP_200_OK: schemas.inspection_filter_response_schema},
    )
    def patch(self, request):
        organisation = self.get_organisation(request.data.get("organisation"))

        try:
            inspection_filter = InspectionFilter.objects.get(organisation=organisation, user=request.user)
            serializer = InspectionFilterSerializer(inspection_filter, data=request.data, partial=True)
        except InspectionFilter.DoesNotExist:
            serializer = InspectionFilterSerializer(data=request.data, context={"user": request.user})

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ProcessingFileCheckView(RetrieveAPIView):
    permission_classes = [AllowAny]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    schema = None

    def remove_domain_from_url(self, url):
        pattern = r"^https?://[^/]+(/.*)$"
        match = re.match(pattern, url)
        if match:
            return match.group(1).lstrip("/")
        return url

    def post(self, request):
        # Parse the incoming Event Grid event
        event_data = json.loads(request.body.decode("utf-8"))
        event = event_data[0]

        if event["eventType"] == "Microsoft.EventGrid.SubscriptionValidationEvent":
            requests.get(event["data"]["validationUrl"])
            return JsonResponse({"validationResponse": event["data"]["validationCode"]}, status=200)

        # Check if the eventType is Microsoft.Storage.BlobCreated
        if event["eventType"] == "Microsoft.Storage.BlobCreated":
            url = event["data"]["url"]
            url_path = self.remove_domain_from_url(url)

            # Query the ProcessingList for a matching file_name
            try:
                processing_item = (
                    ProcessingList.objects.filter(
                        associated_file__url=url_path, upload_completed=False, status="Waiting to process"
                    )
                    .order_by("-id")
                    .first()
                )

                if processing_item is None:
                    return JsonResponse(
                        {"message": f"ProcessingList entry not found for file {url}"},
                        status=404,
                    )

                file_item = FileList.objects.filter(
                    id=processing_item.associated_file.id, upload_completed=False
                ).first()
                if file_item is None:
                    return JsonResponse(
                        {"message": f"ProcessingList entry not found for file {url}"},
                        status=404,
                    )

                blob_client = get_processing_blob_client(
                    file_item.url,
                    container_name=settings.BLOB_STORAGE_VIDEOS_CONTAINER,
                    region=file_item.target_org.country,
                )

                retries = 3
                file_found_in_storage = False
                while retries:
                    if blob_client.exists():
                        file_found_in_storage = True
                        break
                    retries -= 1
                    time.sleep(5)

                if not file_found_in_storage:
                    processing_item.status = "Upload Failed"
                    processing_item.save()
                    return Response(
                        {"error": "Failed to find file in storage - Upload failed"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                file_item.upload_completed = True
                file_item.upload_completed_time = timezone.now()
                file_item.save()

                processing_item.status = "Waiting to process"
                processing_item.upload_completed = True
                processing_item.save()

                enqueue_message(file_item.url, region=file_item.storage_region)

                description = f"Azure BlobCreated webhook fired for {url}"

                AuditList.objects.create(
                    event_type="Update",
                    table="ProcessingList",
                    column="Multi",
                    row_id=processing_item.associated_file.id,
                    description=description,
                    date_of_modification=timezone.now(),
                )

                return JsonResponse({"message": f"ProcessingList updated for file {url}"}, status=200)
            except ProcessingList.DoesNotExist:
                return JsonResponse(
                    {"message": f"ProcessingList entry not found for file {url}"},
                    status=404,
                )
        else:
            return JsonResponse({"message": "Invalid event type"}, status=400)


class FileListView(ListCreateAPIView):
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    pagination_class = StandardResultsSetPagination
    # We never want to show the user hidden files, if we need
    # them from an admin standpoint we can use AllFilesView
    queryset = FileList.objects.filter(hidden=False)

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = [
        "upload_completed_time",
        "created_time",
        "filename",
        "uploaded_by_id",
    ]
    ordering = ["-upload_completed_time", "id"]  # Default ordering is newest first

    filterset_fields = {
        "upload_completed": ["exact"],
        "filename": ["icontains", "exact"],
        "upload_completed_time": ["gte", "lte"],
        "created_time": ["gte", "lte"],
    }

    def get_serializer_class(self):
        if self.request.method == "POST":
            return FileCreateSerializer
        return FileSerializer

    def get_permissions(self):
        if self.request.method == "POST":
            return [IsAuthenticated(), HasOrganisationAccess(), IsServiceUser()]
        return [IsAuthenticated(), HasOrganisationAccess()]

    def get_queryset(self):
        qs = super().get_queryset()
        if isinstance(self.request.user, AnonymousUser):
            return qs.none()  # For schema introspection - real requests have authenticated users

        if self.request.user.is_asset_owner():
            qs = qs.filter(target_org=self.request.user.organisation)

        else:  # Case where contractor
            target_org_id = int(self.request.query_params.get("target_org_id"))
            qs = qs.filter(target_org=target_org_id, upload_org=self.request.user.organisation)

        if self.request.user.user_level == UserLevelEnum.UPLOAD_ONLY:
            qs = qs.filter(uploaded_by=self.request.user)

        include_processing = self.request.query_params.get("include_processing", "false").lower() == "true"
        if not include_processing:
            qs = qs.exclude(processing_completed_time__isnull=True)

        return qs

    def list(self, request, *args, **kwargs):
        target_org_id = request.query_params.get("target_org_id")
        if target_org_id is not None:
            self.check_object_permissions(request, get_object_or_404(Organisations, id=int(target_org_id)))
        return super().list(request, *args, **kwargs)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "target_org_id",
                OpenApiTypes.INT,
                required=False,
                description="The organisation whose files should be listed. Only required if user is a contractor",
            ),
            OpenApiParameter(
                "include_processing",
                OpenApiTypes.BOOL,
                required=False,
                description="Whether to include files that are currently being processed in the results.",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """
        Create a standalone video file record, allowing upload to occur at a later time.
        """
        return super().post(request, *args, **kwargs)


class ProcessingFileRetryView(APIView):
    """
    Retry processing of a file that has already been uploaded.
    """

    permission_classes = [IsAuthenticated, HasOrganisationAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "target_org_id",
                OpenApiTypes.INT,
                required=False,
                description="The organisation whose files should be listed. Only required if user is a contractor",
            ),
        ],
        request=None,
        responses={
            status.HTTP_200_OK: ProcessingFileRetrySerializer(),
            status.HTTP_400_BAD_REQUEST: ProcessingFileRetrySerializer(),
        },
    )
    def post(self, request, *args, **kwargs):
        if request.user.is_asset_owner():
            target_org = request.user.organisation
        else:
            target_org = get_object_or_404(Organisations, id=request.query_params.get("target_org_id"))
            self.check_object_permissions(request, target_org)

        file_id = kwargs.get("file_id")
        file = get_object_or_404(FileList, target_org=target_org, id=file_id)

        if not HasAccessToOrgScopedObject().has_object_permission(
            request, view=self, obj=file, org_field_name="target_org"
        ):
            raise PermissionDenied("User does not have access to the given file")

        try:
            processing_record = file.processing_record
        except ObjectDoesNotExist:
            return Response(
                status=status.HTTP_404_NOT_FOUND,
            )
        if not processing_record.is_retryable:
            return Response(
                ProcessingFileRetrySerializer(instance=processing_record).data,
                status=status.HTTP_400_BAD_REQUEST,
            )

        processing_record.retry()

        AuditList.objects.create(
            event_type="Update",
            table="ProcessingList",
            user_id=request.user.id,
            description=f"Processing of file {file_id} retried",
            column="Multi",
        )

        enqueue_message(file.url, region=file.storage_region)

        return Response(
            data=ProcessingFileRetrySerializer(instance=processing_record).data,
            status=status.HTTP_200_OK,
        )


class AllFilesView(ListAPIView):
    permission_classes = [IsAuthenticated, IsProductOwner]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    pagination_class = StandardResultsSetPagination
    serializer_class = FileSerializer

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = {
        "id": ["exact"],
        "filename": ["exact"],
        "url": ["exact"],
        "file_type": ["exact"],
        "created_time": ["lte", "gte"],
        "target_org_id": ["exact"],
        "upload_org_id": ["exact"],
        "uploaded_by_id": ["exact"],
        "job_id": ["exact"],
        "job_tree_id": ["exact"],
        "hidden": ["exact"],
        "upload_completed": ["exact"],
        "updated_at": ["lte", "gte"],
        "storage_region": ["exact"],
    }
    ordering_fields = [
        "filename",
        "url",
        "file_size",
        "created_time",
        "upload_completed_time",
    ]

    queryset = FileList.objects.all().select_related("job_tree")

    def get_queryset(self):
        qs = super().get_queryset()

        if statuses := self.request.query_params.get("status__in"):
            qs = qs.filter(processing_record__status__in=statuses.split(","))

        if storage_regions := self.request.query_params.get("storage_region__in"):
            qs = qs.filter(storage_region__in=storage_regions.split(","))

        return qs

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "status__in",
                OpenApiTypes.STR,
                required=False,
                description="Filter by a set of comma separated processing statuses",
            ),
            OpenApiParameter(
                "storage_region__in",
                OpenApiTypes.STR,
                required=False,
                description="Filter by a set of comma separated storage regions",
            ),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class InspectionFileDetail(RetrieveUpdateAPIView):
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["get", "patch"]

    queryset = FileList.objects.all()
    lookup_field = "id"

    def get_queryset(self):
        return super().get_queryset().filter(target_org=self.request.organisation)

    def get_permissions(self):
        if self.request.method == "GET":
            return [IsAuthenticated(), HasAccessToOrgScopedObject()]
        return [IsAuthenticated(), HasAccessToOrgScopedObject(), IsServiceUser()]

    def get_serializer_class(self):
        if self.request.method == "GET":
            return FileSerializer
        else:
            return InspectionFilePatchSerializer

    def check_object_permissions(self, request, obj):
        if not HasAccessToOrgScopedObject().has_object_permission(request, self, obj, "target_org"):
            raise PermissionDenied("User does not have access to retrieve or update file metadata")

    @extend_schema(responses={status.HTTP_204_NO_CONTENT: None})
    def patch(self, request, *args, **kwargs):
        super().partial_update(request, *args, **kwargs)
        if request.data.get("upload_completed_time") or request.data.get("upload_completed"):
            instance = self.get_object()
            description = f"File upload complete {instance.filename}"
            AuditList.objects.create(
                event_type="Update",
                table="FileList",
                row_id=instance.id,
                column="upload_completed",
                description=description,
                date_of_modification=timezone.now(),
                user=request.user,
            )

        return Response(status=status.HTTP_204_NO_CONTENT)


class InspectionFileUploadMediaView(CreateAPIView):
    permission_classes = [IsAuthenticated, OrgCanUpload, HasFileAccess]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = InspectionFileUploadMediaSerializer
    queryset = FileList.objects.filter(hidden=False)

    lookup_field = "id"

    def get_queryset(self):
        return super().get_queryset().for_org(self.request.organisation)

    @extend_schema(responses={status.HTTP_200_OK: InspectionFileUploadMediaSerializer()})
    def post(self, request, *args, **kwargs):
        """
        Endpoint for uploading media files to an existing file record.
        """
        file_obj = self.get_object()
        serializer = self.get_serializer(instance=file_obj, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)
