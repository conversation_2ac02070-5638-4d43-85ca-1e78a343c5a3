import uuid

from django.conf import settings
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db import models

from api.inspections.models.files import FileList, VideoFrames


class ChainageUnit(models.TextChoices):
    METRES = "M", "Metres"
    FEET = "FT", "Feet"


class Footage(models.Model):
    """
    The logical representation of footage recorded during an inspection
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    video_file = models.ForeignKey(
        FileList, on_delete=models.SET_NULL, related_name="derived_footage", null=True, default=None
    )
    """
    The physical video file that this footage is derived from.
    Optional as we do not have a video file for some inspections (eg. newly imported).
    """

    chainage_unit = models.CharField(
        max_length=2,
        choices=ChainageUnit.choices,
        default=ChainageUnit.METRES,
    )

    total_frames = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(chainage_unit__in=[v for v, _ in ChainageUnit.choices]),
                name="footage_allowed_chainage_units",
            )
        ]


class Keyframe(models.Model):
    """
    The logical representation of a moment in time in inspection footage
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    footage = models.ForeignKey(Footage, on_delete=models.CASCADE, related_name="keyframes")
    """
    The footage from which this key frame is taken.
    """

    video_frame = models.ForeignKey(
        VideoFrames, null=True, default=None, on_delete=models.SET_NULL, related_name="keyframes"
    )
    """
    The physical image this keyframe is derived from. Optional as we may not have a saved image for every keyframe.
    """

    time_reference_milliseconds = models.PositiveIntegerField()
    sequence_number = models.PositiveIntegerField()
    chainage = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(settings.MAX_CHAINAGE)],
    )
    """
    Distance from the start of the inspection. Unit is specified on the Footage.
    """

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    # Flag fields:
    at_joint = models.BooleanField(default=False)
    has_loss_of_vision = models.BooleanField(default=False)
    has_textbox = models.BooleanField(default=False)
    has_title = models.BooleanField(default=False)

    class Meta:
        ordering = ["footage", "sequence_number"]
        constraints = [
            models.UniqueConstraint(
                fields=["footage", "sequence_number"], name="footage_keyframe_unique_sequence_number"
            ),
        ]
